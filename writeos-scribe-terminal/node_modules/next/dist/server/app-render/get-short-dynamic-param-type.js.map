{"version": 3, "sources": ["../../../src/server/app-render/get-short-dynamic-param-type.tsx"], "sourcesContent": ["import type { DynamicParamTypes, DynamicParamTypesShort } from './types'\n\nexport const dynamicParamTypes: Record<\n  DynamicParamTypes,\n  DynamicParamTypesShort\n> = {\n  catchall: 'c',\n  'catchall-intercepted': 'ci',\n  'optional-catchall': 'oc',\n  dynamic: 'd',\n  'dynamic-intercepted': 'di',\n}\n\n/**\n * Shorten the dynamic param in order to make it smaller when transmitted to the browser.\n */\nexport function getShortDynamicParamType(\n  type: DynamicParamTypes\n): DynamicParamTypesShort {\n  const short = dynamicParamTypes[type]\n  if (!short) {\n    throw new Error('Unknown dynamic param type')\n  }\n  return short\n}\n"], "names": ["dynamicParamTypes", "getShortDynamicParamType", "catchall", "dynamic", "type", "short", "Error"], "mappings": ";;;;;;;;;;;;;;;IAEaA,iBAAiB;eAAjBA;;IAcGC,wBAAwB;eAAxBA;;;AAdT,MAAMD,oBAGT;IACFE,UAAU;IACV,wBAAwB;IACxB,qBAAqB;IACrBC,SAAS;IACT,uBAAuB;AACzB;AAKO,SAASF,yBACdG,IAAuB;IAEvB,MAAMC,QAAQL,iBAAiB,CAACI,KAAK;IACrC,IAAI,CAACC,OAAO;QACV,MAAM,qBAAuC,CAAvC,IAAIC,MAAM,+BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsC;IAC9C;IACA,OAAOD;AACT"}