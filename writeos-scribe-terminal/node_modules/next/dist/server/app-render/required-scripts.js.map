{"version": 3, "sources": ["../../../src/server/app-render/required-scripts.tsx"], "sourcesContent": ["import { encodeURIPath } from '../../shared/lib/encode-uri-path'\nimport type { BuildManifest } from '../get-page-files'\n\nimport ReactDOM from 'react-dom'\n\nexport function getRequiredScripts(\n  buildManifest: BuildManifest,\n  assetPrefix: string,\n  crossOrigin: undefined | '' | 'anonymous' | 'use-credentials',\n  SRIManifest: undefined | Record<string, string>,\n  qs: string,\n  nonce: string | undefined,\n  pagePath: string\n): [\n  () => void,\n  { src: string; integrity?: string; crossOrigin?: string | undefined },\n] {\n  let preinitScripts: () => void\n  let preinitScriptCommands: string[] = []\n  const bootstrapScript: {\n    src: string\n    integrity?: string\n    crossOrigin?: string | undefined\n  } = {\n    src: '',\n    crossOrigin,\n  }\n\n  const files = (\n    buildManifest.rootMainFilesTree?.[pagePath] || buildManifest.rootMainFiles\n  ).map(encodeURIPath)\n  if (files.length === 0) {\n    throw new Error(\n      'Invariant: missing bootstrap script. This is a bug in Next.js'\n    )\n  }\n  if (SRIManifest) {\n    bootstrapScript.src = `${assetPrefix}/_next/` + files[0] + qs\n    bootstrapScript.integrity = SRIManifest[files[0]]\n\n    for (let i = 1; i < files.length; i++) {\n      const src = `${assetPrefix}/_next/` + files[i] + qs\n      const integrity = SRIManifest[files[i]]\n      preinitScriptCommands.push(src, integrity)\n    }\n    preinitScripts = () => {\n      // preinitScriptCommands is a double indexed array of src/integrity pairs\n      for (let i = 0; i < preinitScriptCommands.length; i += 2) {\n        ReactDOM.preinit(preinitScriptCommands[i], {\n          as: 'script',\n          integrity: preinitScriptCommands[i + 1],\n          crossOrigin,\n          nonce,\n        })\n      }\n    }\n  } else {\n    bootstrapScript.src = `${assetPrefix}/_next/` + files[0] + qs\n\n    for (let i = 1; i < files.length; i++) {\n      const src = `${assetPrefix}/_next/` + files[i] + qs\n      preinitScriptCommands.push(src)\n    }\n    preinitScripts = () => {\n      // preinitScriptCommands is a singled indexed array of src values\n      for (let i = 0; i < preinitScriptCommands.length; i++) {\n        ReactDOM.preinit(preinitScriptCommands[i], {\n          as: 'script',\n          nonce,\n          crossOrigin,\n        })\n      }\n    }\n  }\n\n  return [preinitScripts, bootstrapScript]\n}\n"], "names": ["getRequiredScripts", "buildManifest", "assetPrefix", "crossOrigin", "SRIManifest", "qs", "nonce", "pagePath", "preinitScripts", "preinitScriptCommands", "bootstrapScript", "src", "files", "rootMainFilesTree", "rootMainFiles", "map", "encodeURIPath", "length", "Error", "integrity", "i", "push", "ReactDOM", "preinit", "as"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;+BALc;iEAGT;;;;;;AAEd,SAASA,mBACdC,aAA4B,EAC5BC,WAAmB,EACnBC,WAA6D,EAC7DC,WAA+C,EAC/CC,EAAU,EACVC,KAAyB,EACzBC,QAAgB;QAiBdN;IAZF,IAAIO;IACJ,IAAIC,wBAAkC,EAAE;IACxC,MAAMC,kBAIF;QACFC,KAAK;QACLR;IACF;IAEA,MAAMS,QAAQ,AACZX,CAAAA,EAAAA,mCAAAA,cAAcY,iBAAiB,qBAA/BZ,gCAAiC,CAACM,SAAS,KAAIN,cAAca,aAAa,AAAD,EACzEC,GAAG,CAACC,4BAAa;IACnB,IAAIJ,MAAMK,MAAM,KAAK,GAAG;QACtB,MAAM,qBAEL,CAFK,IAAIC,MACR,kEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,IAAId,aAAa;QACfM,gBAAgBC,GAAG,GAAG,GAAGT,YAAY,OAAO,CAAC,GAAGU,KAAK,CAAC,EAAE,GAAGP;QAC3DK,gBAAgBS,SAAS,GAAGf,WAAW,CAACQ,KAAK,CAAC,EAAE,CAAC;QAEjD,IAAK,IAAIQ,IAAI,GAAGA,IAAIR,MAAMK,MAAM,EAAEG,IAAK;YACrC,MAAMT,MAAM,GAAGT,YAAY,OAAO,CAAC,GAAGU,KAAK,CAACQ,EAAE,GAAGf;YACjD,MAAMc,YAAYf,WAAW,CAACQ,KAAK,CAACQ,EAAE,CAAC;YACvCX,sBAAsBY,IAAI,CAACV,KAAKQ;QAClC;QACAX,iBAAiB;YACf,yEAAyE;YACzE,IAAK,IAAIY,IAAI,GAAGA,IAAIX,sBAAsBQ,MAAM,EAAEG,KAAK,EAAG;gBACxDE,iBAAQ,CAACC,OAAO,CAACd,qBAAqB,CAACW,EAAE,EAAE;oBACzCI,IAAI;oBACJL,WAAWV,qBAAqB,CAACW,IAAI,EAAE;oBACvCjB;oBACAG;gBACF;YACF;QACF;IACF,OAAO;QACLI,gBAAgBC,GAAG,GAAG,GAAGT,YAAY,OAAO,CAAC,GAAGU,KAAK,CAAC,EAAE,GAAGP;QAE3D,IAAK,IAAIe,IAAI,GAAGA,IAAIR,MAAMK,MAAM,EAAEG,IAAK;YACrC,MAAMT,MAAM,GAAGT,YAAY,OAAO,CAAC,GAAGU,KAAK,CAACQ,EAAE,GAAGf;YACjDI,sBAAsBY,IAAI,CAACV;QAC7B;QACAH,iBAAiB;YACf,iEAAiE;YACjE,IAAK,IAAIY,IAAI,GAAGA,IAAIX,sBAAsBQ,MAAM,EAAEG,IAAK;gBACrDE,iBAAQ,CAACC,OAAO,CAACd,qBAAqB,CAACW,EAAE,EAAE;oBACzCI,IAAI;oBACJlB;oBACAH;gBACF;YACF;QACF;IACF;IAEA,OAAO;QAACK;QAAgBE;KAAgB;AAC1C"}