{"version": 3, "sources": ["../../../src/server/app-render/encryption-utils-server.ts"], "sourcesContent": ["// This file should never be bundled into application's runtime code and should\n// stay in the Next.js server.\nimport path from 'path'\nimport fs from 'fs'\nimport { getStorageDirectory } from '../cache-dir'\nimport { arrayBufferToString } from './encryption-utils'\n\n// Keep the key in memory as it should never change during the lifetime of the server in\n// both development and production.\nlet __next_encryption_key_generation_promise: Promise<string> | null = null\nconst CONFIG_FILE = '.rscinfo'\nconst ENCRYPTION_KEY = 'encryption.key'\nconst ENCRYPTION_EXPIRE_AT = 'encryption.expire_at'\nconst EXPIRATION = 1000 * 60 * 60 * 24 * 14 // 14 days\n\nasync function writeCache(distDir: string, configValue: string) {\n  const cacheBaseDir = getStorageDirectory(distDir)\n  if (!cacheBaseDir) return\n\n  const configPath = path.join(cacheBaseDir, CONFIG_FILE)\n  if (!fs.existsSync(cacheBaseDir)) {\n    await fs.promises.mkdir(cacheBaseDir, { recursive: true })\n  }\n  await fs.promises.writeFile(\n    configPath,\n    JSON.stringify({\n      [ENCRYPTION_KEY]: configValue,\n      [ENCRYPTION_EXPIRE_AT]: Date.now() + EXPIRATION,\n    })\n  )\n}\n\n// This utility is used to get a key for the cache directory. If the\n// key is not present, it will generate a new one and store it in the\n// cache directory inside dist.\n// The key will also expire after a certain amount of time. Once it\n// expires, a new one will be generated.\n// During the lifetime of the server, it will be reused and never refreshed.\nasync function loadOrGenerateKey(\n  distDir: string,\n  isBuild: boolean,\n  generateKey: () => Promise<string>\n): Promise<string> {\n  const cacheBaseDir = getStorageDirectory(distDir)\n\n  if (!cacheBaseDir) {\n    // There's no persistent storage available. We generate a new key.\n    // This also covers development time.\n    return await generateKey()\n  }\n\n  const configPath = path.join(cacheBaseDir, CONFIG_FILE)\n  async function hasCachedKey(): Promise<false | string> {\n    if (!fs.existsSync(configPath)) return false\n    try {\n      const config = JSON.parse(await fs.promises.readFile(configPath, 'utf8'))\n      if (!config) return false\n      if (\n        typeof config[ENCRYPTION_KEY] !== 'string' ||\n        typeof config[ENCRYPTION_EXPIRE_AT] !== 'number'\n      ) {\n        return false\n      }\n      // For build time, we need to rotate the key if it's expired. Otherwise\n      // (next start) we have to keep the key as it is so the runtime key matches\n      // the build time key.\n      if (isBuild && config[ENCRYPTION_EXPIRE_AT] < Date.now()) {\n        return false\n      }\n      const cachedKey = config[ENCRYPTION_KEY]\n\n      // If encryption key is provided via env, and it's not same as valid cache,\n      //  we should not use the cached key and respect the env key.\n      if (\n        cachedKey &&\n        process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY &&\n        cachedKey !== process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\n      ) {\n        return false\n      }\n      return cachedKey\n    } catch {\n      // Broken config file. We should generate a new key and overwrite it.\n      return false\n    }\n  }\n  const maybeValidKey = await hasCachedKey()\n  if (typeof maybeValidKey === 'string') {\n    return maybeValidKey\n  }\n  const key = await generateKey()\n  await writeCache(distDir, key)\n\n  return key\n}\n\nexport async function generateEncryptionKeyBase64({\n  isBuild,\n  distDir,\n}: {\n  isBuild: boolean\n  distDir: string\n}) {\n  // This avoids it being generated multiple times in parallel.\n  if (!__next_encryption_key_generation_promise) {\n    __next_encryption_key_generation_promise = loadOrGenerateKey(\n      distDir,\n      isBuild,\n      async () => {\n        const providedKey = process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\n\n        if (providedKey) {\n          return providedKey\n        }\n        const key = await crypto.subtle.generateKey(\n          {\n            name: 'AES-GCM',\n            length: 256,\n          },\n          true,\n          ['encrypt', 'decrypt']\n        )\n        const exported = await crypto.subtle.exportKey('raw', key)\n        return btoa(arrayBufferToString(exported))\n      }\n    )\n  }\n  return __next_encryption_key_generation_promise\n}\n"], "names": ["generateEncryptionKeyBase64", "__next_encryption_key_generation_promise", "CONFIG_FILE", "ENCRYPTION_KEY", "ENCRYPTION_EXPIRE_AT", "EXPIRATION", "writeCache", "distDir", "config<PERSON><PERSON><PERSON>", "cacheBaseDir", "getStorageDirectory", "config<PERSON><PERSON>", "path", "join", "fs", "existsSync", "promises", "mkdir", "recursive", "writeFile", "JSON", "stringify", "Date", "now", "loadOrGenerateKey", "isBuild", "<PERSON><PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "config", "parse", "readFile", "cachedKey", "process", "env", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "<PERSON><PERSON><PERSON>", "crypto", "subtle", "name", "length", "exported", "exportKey", "btoa", "arrayBufferToString"], "mappings": "AAAA,+EAA+E;AAC/E,8BAA8B;;;;;+BA+FRA;;;eAAAA;;;6DA9FL;2DACF;0BACqB;iCACA;;;;;;AAEpC,wFAAwF;AACxF,mCAAmC;AACnC,IAAIC,2CAAmE;AACvE,MAAMC,cAAc;AACpB,MAAMC,iBAAiB;AACvB,MAAMC,uBAAuB;AAC7B,MAAMC,aAAa,OAAO,KAAK,KAAK,KAAK,GAAG,UAAU;;AAEtD,eAAeC,WAAWC,OAAe,EAAEC,WAAmB;IAC5D,MAAMC,eAAeC,IAAAA,6BAAmB,EAACH;IACzC,IAAI,CAACE,cAAc;IAEnB,MAAME,aAAaC,aAAI,CAACC,IAAI,CAACJ,cAAcP;IAC3C,IAAI,CAACY,WAAE,CAACC,UAAU,CAACN,eAAe;QAChC,MAAMK,WAAE,CAACE,QAAQ,CAACC,KAAK,CAACR,cAAc;YAAES,WAAW;QAAK;IAC1D;IACA,MAAMJ,WAAE,CAACE,QAAQ,CAACG,SAAS,CACzBR,YACAS,KAAKC,SAAS,CAAC;QACb,CAAClB,eAAe,EAAEK;QAClB,CAACJ,qBAAqB,EAAEkB,KAAKC,GAAG,KAAKlB;IACvC;AAEJ;AAEA,oEAAoE;AACpE,qEAAqE;AACrE,+BAA+B;AAC/B,mEAAmE;AACnE,wCAAwC;AACxC,4EAA4E;AAC5E,eAAemB,kBACbjB,OAAe,EACfkB,OAAgB,EAChBC,WAAkC;IAElC,MAAMjB,eAAeC,IAAAA,6BAAmB,EAACH;IAEzC,IAAI,CAACE,cAAc;QACjB,kEAAkE;QAClE,qCAAqC;QACrC,OAAO,MAAMiB;IACf;IAEA,MAAMf,aAAaC,aAAI,CAACC,IAAI,CAACJ,cAAcP;IAC3C,eAAeyB;QACb,IAAI,CAACb,WAAE,CAACC,UAAU,CAACJ,aAAa,OAAO;QACvC,IAAI;YACF,MAAMiB,SAASR,KAAKS,KAAK,CAAC,MAAMf,WAAE,CAACE,QAAQ,CAACc,QAAQ,CAACnB,YAAY;YACjE,IAAI,CAACiB,QAAQ,OAAO;YACpB,IACE,OAAOA,MAAM,CAACzB,eAAe,KAAK,YAClC,OAAOyB,MAAM,CAACxB,qBAAqB,KAAK,UACxC;gBACA,OAAO;YACT;YACA,uEAAuE;YACvE,2EAA2E;YAC3E,sBAAsB;YACtB,IAAIqB,WAAWG,MAAM,CAACxB,qBAAqB,GAAGkB,KAAKC,GAAG,IAAI;gBACxD,OAAO;YACT;YACA,MAAMQ,YAAYH,MAAM,CAACzB,eAAe;YAExC,2EAA2E;YAC3E,6DAA6D;YAC7D,IACE4B,aACAC,QAAQC,GAAG,CAACC,kCAAkC,IAC9CH,cAAcC,QAAQC,GAAG,CAACC,kCAAkC,EAC5D;gBACA,OAAO;YACT;YACA,OAAOH;QACT,EAAE,OAAM;YACN,qEAAqE;YACrE,OAAO;QACT;IACF;IACA,MAAMI,gBAAgB,MAAMR;IAC5B,IAAI,OAAOQ,kBAAkB,UAAU;QACrC,OAAOA;IACT;IACA,MAAMC,MAAM,MAAMV;IAClB,MAAMpB,WAAWC,SAAS6B;IAE1B,OAAOA;AACT;AAEO,eAAepC,4BAA4B,EAChDyB,OAAO,EACPlB,OAAO,EAIR;IACC,6DAA6D;IAC7D,IAAI,CAACN,0CAA0C;QAC7CA,2CAA2CuB,kBACzCjB,SACAkB,SACA;YACE,MAAMY,cAAcL,QAAQC,GAAG,CAACC,kCAAkC;YAElE,IAAIG,aAAa;gBACf,OAAOA;YACT;YACA,MAAMD,MAAM,MAAME,OAAOC,MAAM,CAACb,WAAW,CACzC;gBACEc,MAAM;gBACNC,QAAQ;YACV,GACA,MACA;gBAAC;gBAAW;aAAU;YAExB,MAAMC,WAAW,MAAMJ,OAAOC,MAAM,CAACI,SAAS,CAAC,OAAOP;YACtD,OAAOQ,KAAKC,IAAAA,oCAAmB,EAACH;QAClC;IAEJ;IACA,OAAOzC;AACT"}