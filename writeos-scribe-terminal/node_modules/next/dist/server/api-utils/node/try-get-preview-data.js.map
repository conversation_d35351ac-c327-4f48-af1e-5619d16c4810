{"version": 3, "sources": ["../../../../src/server/api-utils/node/try-get-preview-data.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextApiResponse } from '../../../shared/lib/utils'\nimport { checkIsOnDemandRevalidate } from '../.'\nimport type { __ApiPreviewProps } from '../.'\nimport type { BaseNextRequest, BaseNextResponse } from '../../base-http'\nimport type { PreviewData } from '../../../types'\n\nimport {\n  clearPreviewData,\n  COOKIE_NAME_PRERENDER_BYPASS,\n  COOKIE_NAME_PRERENDER_DATA,\n  SYMBOL_PREVIEW_DATA,\n} from '../index'\nimport { RequestCookies } from '../../web/spec-extension/cookies'\nimport { HeadersAdapter } from '../../web/spec-extension/adapters/headers'\n\nexport function tryGetPreviewData(\n  req: IncomingMessage | BaseNextRequest | Request,\n  res: ServerResponse | BaseNextResponse,\n  options: __ApiPreviewProps,\n  multiZoneDraftMode: boolean\n): PreviewData {\n  // if an On-Demand revalidation is being done preview mode\n  // is disabled\n  if (options && checkIsOnDemandRevalidate(req, options).isOnDemandRevalidate) {\n    return false\n  }\n\n  // Read cached preview data if present\n  // TODO: use request metadata instead of a symbol\n  if (SYMBOL_PREVIEW_DATA in req) {\n    return (req as any)[SYMBOL_PREVIEW_DATA] as any\n  }\n\n  const headers = HeadersAdapter.from(req.headers)\n  const cookies = new RequestCookies(headers)\n\n  const previewModeId = cookies.get(COOKIE_NAME_PRERENDER_BYPASS)?.value\n  const tokenPreviewData = cookies.get(COOKIE_NAME_PRERENDER_DATA)?.value\n\n  // Case: preview mode cookie set but data cookie is not set\n  if (\n    previewModeId &&\n    !tokenPreviewData &&\n    previewModeId === options.previewModeId\n  ) {\n    // This is \"Draft Mode\" which doesn't use\n    // previewData, so we return an empty object\n    // for backwards compat with \"Preview Mode\".\n    const data = {}\n    Object.defineProperty(req, SYMBOL_PREVIEW_DATA, {\n      value: data,\n      enumerable: false,\n    })\n    return data\n  }\n\n  // Case: neither cookie is set.\n  if (!previewModeId && !tokenPreviewData) {\n    return false\n  }\n\n  // Case: one cookie is set, but not the other.\n  if (!previewModeId || !tokenPreviewData) {\n    if (!multiZoneDraftMode) {\n      clearPreviewData(res as NextApiResponse)\n    }\n    return false\n  }\n\n  // Case: preview session is for an old build.\n  if (previewModeId !== options.previewModeId) {\n    if (!multiZoneDraftMode) {\n      clearPreviewData(res as NextApiResponse)\n    }\n    return false\n  }\n\n  let encryptedPreviewData: {\n    data: string\n  }\n  try {\n    const jsonwebtoken =\n      require('next/dist/compiled/jsonwebtoken') as typeof import('next/dist/compiled/jsonwebtoken')\n    encryptedPreviewData = jsonwebtoken.verify(\n      tokenPreviewData,\n      options.previewModeSigningKey\n    ) as typeof encryptedPreviewData\n  } catch {\n    // TODO: warn\n    clearPreviewData(res as NextApiResponse)\n    return false\n  }\n\n  const { decryptWithSecret } =\n    require('../../crypto-utils') as typeof import('../../crypto-utils')\n  const decryptedPreviewData = decryptWithSecret(\n    Buffer.from(options.previewModeEncryptionKey),\n    encryptedPreviewData.data\n  )\n\n  try {\n    // TODO: strict runtime type checking\n    const data = JSON.parse(decryptedPreviewData)\n    // Cache lookup\n    Object.defineProperty(req, SYMBOL_PREVIEW_DATA, {\n      value: data,\n      enumerable: false,\n    })\n    return data\n  } catch {\n    return false\n  }\n}\n"], "names": ["tryGetPreviewData", "req", "res", "options", "multiZoneDraftMode", "cookies", "checkIsOnDemandRevalidate", "isOnDemandRevalidate", "SYMBOL_PREVIEW_DATA", "headers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "RequestCookies", "previewModeId", "get", "COOKIE_NAME_PRERENDER_BYPASS", "value", "tokenPreviewData", "COOKIE_NAME_PRERENDER_DATA", "data", "Object", "defineProperty", "enumerable", "clearPreviewData", "encryptedPreviewData", "jsonwebtoken", "require", "verify", "previewModeSigningKey", "decryptWithSecret", "decryptedPreviewData", "<PERSON><PERSON><PERSON>", "previewModeEncryptionKey", "JSON", "parse"], "mappings": ";;;;+BAgBgBA;;;eAAAA;;;kBAd0B;uBAUnC;yBACwB;yBACA;AAExB,SAASA,kBACdC,GAAgD,EAChDC,GAAsC,EACtCC,OAA0B,EAC1BC,kBAA2B;QAiBLC,cACGA;IAhBzB,0DAA0D;IAC1D,cAAc;IACd,IAAIF,WAAWG,IAAAA,2BAAyB,EAACL,KAAKE,SAASI,oBAAoB,EAAE;QAC3E,OAAO;IACT;IAEA,sCAAsC;IACtC,iDAAiD;IACjD,IAAIC,0BAAmB,IAAIP,KAAK;QAC9B,OAAO,AAACA,GAAW,CAACO,0BAAmB,CAAC;IAC1C;IAEA,MAAMC,UAAUC,uBAAc,CAACC,IAAI,CAACV,IAAIQ,OAAO;IAC/C,MAAMJ,UAAU,IAAIO,uBAAc,CAACH;IAEnC,MAAMI,iBAAgBR,eAAAA,QAAQS,GAAG,CAACC,mCAA4B,sBAAxCV,aAA2CW,KAAK;IACtE,MAAMC,oBAAmBZ,gBAAAA,QAAQS,GAAG,CAACI,iCAA0B,sBAAtCb,cAAyCW,KAAK;IAEvE,2DAA2D;IAC3D,IACEH,iBACA,CAACI,oBACDJ,kBAAkBV,QAAQU,aAAa,EACvC;QACA,yCAAyC;QACzC,4CAA4C;QAC5C,4CAA4C;QAC5C,MAAMM,OAAO,CAAC;QACdC,OAAOC,cAAc,CAACpB,KAAKO,0BAAmB,EAAE;YAC9CQ,OAAOG;YACPG,YAAY;QACd;QACA,OAAOH;IACT;IAEA,+BAA+B;IAC/B,IAAI,CAACN,iBAAiB,CAACI,kBAAkB;QACvC,OAAO;IACT;IAEA,8CAA8C;IAC9C,IAAI,CAACJ,iBAAiB,CAACI,kBAAkB;QACvC,IAAI,CAACb,oBAAoB;YACvBmB,IAAAA,uBAAgB,EAACrB;QACnB;QACA,OAAO;IACT;IAEA,6CAA6C;IAC7C,IAAIW,kBAAkBV,QAAQU,aAAa,EAAE;QAC3C,IAAI,CAACT,oBAAoB;YACvBmB,IAAAA,uBAAgB,EAACrB;QACnB;QACA,OAAO;IACT;IAEA,IAAIsB;IAGJ,IAAI;QACF,MAAMC,eACJC,QAAQ;QACVF,uBAAuBC,aAAaE,MAAM,CACxCV,kBACAd,QAAQyB,qBAAqB;IAEjC,EAAE,OAAM;QACN,aAAa;QACbL,IAAAA,uBAAgB,EAACrB;QACjB,OAAO;IACT;IAEA,MAAM,EAAE2B,iBAAiB,EAAE,GACzBH,QAAQ;IACV,MAAMI,uBAAuBD,kBAC3BE,OAAOpB,IAAI,CAACR,QAAQ6B,wBAAwB,GAC5CR,qBAAqBL,IAAI;IAG3B,IAAI;QACF,qCAAqC;QACrC,MAAMA,OAAOc,KAAKC,KAAK,CAACJ;QACxB,eAAe;QACfV,OAAOC,cAAc,CAACpB,KAAKO,0BAAmB,EAAE;YAC9CQ,OAAOG;YACPG,YAAY;QACd;QACA,OAAOH;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF"}