{"version": 3, "sources": ["../../../../src/server/api-utils/node/parse-body.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\n\nimport { parse } from 'next/dist/compiled/content-type'\nimport isError from '../../../lib/is-error'\nimport type { SizeLimit } from '../../../types'\nimport { ApiError } from '../index'\n\n/**\n * Parse `JSON` and handles invalid `JSON` strings\n * @param str `JSON` string\n */\nfunction parseJson(str: string): object {\n  if (str.length === 0) {\n    // special-case empty json body, as it's a common client-side mistake\n    return {}\n  }\n\n  try {\n    return JSON.parse(str)\n  } catch (e) {\n    throw new ApiError(400, 'Invalid JSON')\n  }\n}\n\n/**\n * Parse incoming message like `json` or `urlencoded`\n * @param req request object\n */\nexport async function parseBody(\n  req: IncomingMessage,\n  limit: SizeLimit\n): Promise<any> {\n  let contentType\n  try {\n    contentType = parse(req.headers['content-type'] || 'text/plain')\n  } catch {\n    contentType = parse('text/plain')\n  }\n  const { type, parameters } = contentType\n  const encoding = parameters.charset || 'utf-8'\n\n  let buffer\n\n  try {\n    const getRawBody =\n      require('next/dist/compiled/raw-body') as typeof import('next/dist/compiled/raw-body')\n    buffer = await getRawBody(req, { encoding, limit })\n  } catch (e) {\n    if (isError(e) && e.type === 'entity.too.large') {\n      throw new ApiError(413, `Body exceeded ${limit} limit`)\n    } else {\n      throw new ApiError(400, 'Invalid body')\n    }\n  }\n\n  const body = buffer.toString()\n\n  if (type === 'application/json' || type === 'application/ld+json') {\n    return parseJson(body)\n  } else if (type === 'application/x-www-form-urlencoded') {\n    const qs = require('querystring')\n    return qs.decode(body)\n  } else {\n    return body\n  }\n}\n"], "names": ["parseBody", "parseJson", "str", "length", "JSON", "parse", "e", "ApiError", "req", "limit", "contentType", "headers", "type", "parameters", "encoding", "charset", "buffer", "getRawBody", "require", "isError", "body", "toString", "qs", "decode"], "mappings": ";;;;+BA4BsBA;;;eAAAA;;;6BA1BA;gEACF;uBAEK;;;;;;AAEzB;;;CAGC,GACD,SAASC,UAAUC,GAAW;IAC5B,IAAIA,IAAIC,MAAM,KAAK,GAAG;QACpB,qEAAqE;QACrE,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAOC,KAAKC,KAAK,CAACH;IACpB,EAAE,OAAOI,GAAG;QACV,MAAM,qBAAiC,CAAjC,IAAIC,eAAQ,CAAC,KAAK,iBAAlB,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;AACF;AAMO,eAAeP,UACpBQ,GAAoB,EACpBC,KAAgB;IAEhB,IAAIC;IACJ,IAAI;QACFA,cAAcL,IAAAA,kBAAK,EAACG,IAAIG,OAAO,CAAC,eAAe,IAAI;IACrD,EAAE,OAAM;QACND,cAAcL,IAAAA,kBAAK,EAAC;IACtB;IACA,MAAM,EAAEO,IAAI,EAAEC,UAAU,EAAE,GAAGH;IAC7B,MAAMI,WAAWD,WAAWE,OAAO,IAAI;IAEvC,IAAIC;IAEJ,IAAI;QACF,MAAMC,aACJC,QAAQ;QACVF,SAAS,MAAMC,WAAWT,KAAK;YAAEM;YAAUL;QAAM;IACnD,EAAE,OAAOH,GAAG;QACV,IAAIa,IAAAA,gBAAO,EAACb,MAAMA,EAAEM,IAAI,KAAK,oBAAoB;YAC/C,MAAM,qBAAiD,CAAjD,IAAIL,eAAQ,CAAC,KAAK,CAAC,cAAc,EAAEE,MAAM,MAAM,CAAC,GAAhD,qBAAA;uBAAA;4BAAA;8BAAA;YAAgD;QACxD,OAAO;YACL,MAAM,qBAAiC,CAAjC,IAAIF,eAAQ,CAAC,KAAK,iBAAlB,qBAAA;uBAAA;4BAAA;8BAAA;YAAgC;QACxC;IACF;IAEA,MAAMa,OAAOJ,OAAOK,QAAQ;IAE5B,IAAIT,SAAS,sBAAsBA,SAAS,uBAAuB;QACjE,OAAOX,UAAUmB;IACnB,OAAO,IAAIR,SAAS,qCAAqC;QACvD,MAAMU,KAAKJ,QAAQ;QACnB,OAAOI,GAAGC,MAAM,CAACH;IACnB,OAAO;QACL,OAAOA;IACT;AACF"}