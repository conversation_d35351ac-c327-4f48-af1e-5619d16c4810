{"version": 3, "sources": ["../../../src/server/api-utils/web.ts"], "sourcesContent": ["// Buffer.byteLength polyfill in the Edge runtime, with only utf8 strings\n// supported at the moment.\nexport function byteLength(payload: string): number {\n  return new TextEncoder().encode(payload).buffer.byteLength\n}\n"], "names": ["byteLength", "payload", "TextEncoder", "encode", "buffer"], "mappings": "AAAA,yEAAyE;AACzE,2BAA2B;;;;;+BACXA;;;eAAAA;;;AAAT,SAASA,WAAWC,OAAe;IACxC,OAAO,IAAIC,cAAcC,MAAM,CAACF,SAASG,MAAM,CAACJ,UAAU;AAC5D"}