{"version": 3, "sources": ["../../../src/shared/lib/get-webpack-bundler.ts"], "sourcesContent": ["import { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { getRspackCore } from './get-rspack'\n\n/**\n * Depending on if Rspack is active or not, returns the appropriate set of\n * webpack-compatible api.\n *\n * @returns webpack bundler\n */\nexport default function getWebpackBundler(): typeof webpack {\n  return process.env.NEXT_RSPACK ? getRspackCore() : webpack\n}\n"], "names": ["getWebpackBundler", "process", "env", "NEXT_RSPACK", "getRspackCore", "webpack"], "mappings": ";;;;+BAGA;;;;;CAKC,GACD;;;eAAwBA;;;yBATA;2BACM;AAQf,SAASA;IACtB,OAAOC,QAAQC,GAAG,CAACC,WAAW,GAAGC,IAAAA,wBAAa,MAAKC,gBAAO;AAC5D"}