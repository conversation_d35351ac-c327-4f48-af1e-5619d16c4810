/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_ghost_in_the_wire_Documents_augment_projects_writeos_scribe_terminal_writeos_scribe_terminal_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/chat/route.ts */ \"(rsc)/./app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/api/chat/route.ts\",\n    nextConfigOutput,\n    userland: _home_ghost_in_the_wire_Documents_augment_projects_writeos_scribe_terminal_writeos_scribe_terminal_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*******************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*******************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/chat/route.ts":
/*!*******************************!*\
  !*** ./app/api/chat/route.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/ai-service */ \"(rsc)/./src/lib/ai-service.ts\");\n\n\nasync function POST(request) {\n    try {\n        const { message, history, context } = await request.json();\n        if (!message || !message.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Message is required'\n            }, {\n                status: 400\n            });\n        }\n        // Phase III - Real AI Chat Integration\n        const aiService = _lib_ai_service__WEBPACK_IMPORTED_MODULE_1__.AIService.getInstance();\n        // Convert history to the format expected by AI service\n        const chatHistory = history?.map((h)=>({\n                role: h.role,\n                content: h.content,\n                timestamp: new Date(h.timestamp)\n            }));\n        const startTime = Date.now();\n        const response = await aiService.chat(message.trim(), chatHistory);\n        const processingTime = Date.now() - startTime;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: response,\n            metadata: {\n                processingTime,\n                aiProvider: aiService.getProviderName(),\n                timestamp: new Date().toISOString(),\n                messageLength: response.length,\n                contextUsed: !!context\n            }\n        });\n    } catch (error) {\n        console.error('Chat API error:', error);\n        // Fallback response\n        const fallbackResponses = [\n            \"I'm here to help with your writing. What would you like to work on?\",\n            \"Let's craft something amazing together. What's on your mind?\",\n            \"I can assist with writing, editing, brainstorming, or structuring. How can I help?\",\n            \"That's an interesting question! Could you provide more context about what you're working on?\",\n            \"I'd be happy to help you improve your writing. What specific area would you like to focus on?\"\n        ];\n        const fallbackResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: fallbackResponse,\n            metadata: {\n                processingTime: 100,\n                aiProvider: 'Fallback System',\n                timestamp: new Date().toISOString(),\n                messageLength: fallbackResponse.length,\n                contextUsed: false,\n                fallback: true\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ai-service.ts":
/*!*******************************!*\
  !*** ./src/lib/ai-service.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIService: () => (/* binding */ AIService)\n/* harmony export */ });\nclass OpenAIProvider {\n    constructor(apiKey){\n        this.name = 'OpenAI GPT-4';\n        this.apiKey = apiKey;\n    }\n    async enhance(content, context) {\n        if (!this.apiKey || this.apiKey === 'your_openai_api_key_here') {\n            return this.fallbackEnhance(content);\n        }\n        try {\n            const response = await fetch('https://api.openai.com/v1/chat/completions', {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${this.apiKey}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: 'gpt-4',\n                    messages: [\n                        {\n                            role: 'system',\n                            content: 'You are a professional writing assistant. Enhance the given text by improving word choice, sentence structure, and clarity while maintaining the original meaning and tone.'\n                        },\n                        {\n                            role: 'user',\n                            content: `Please enhance this text: ${content}`\n                        }\n                    ],\n                    max_tokens: 1000,\n                    temperature: 0.7\n                })\n            });\n            const data = await response.json();\n            return data.choices[0]?.message?.content || content;\n        } catch (error) {\n            console.error('OpenAI API error:', error);\n            return this.fallbackEnhance(content);\n        }\n    }\n    async summarize(content, context) {\n        if (!this.apiKey || this.apiKey === 'your_openai_api_key_here') {\n            return this.fallbackSummarize(content);\n        }\n        try {\n            const response = await fetch('https://api.openai.com/v1/chat/completions', {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${this.apiKey}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: 'gpt-4',\n                    messages: [\n                        {\n                            role: 'system',\n                            content: 'You are a professional summarization assistant. Create concise, well-structured summaries that capture the key points and main ideas.'\n                        },\n                        {\n                            role: 'user',\n                            content: `Please summarize this text: ${content}`\n                        }\n                    ],\n                    max_tokens: 500,\n                    temperature: 0.3\n                })\n            });\n            const data = await response.json();\n            return data.choices[0]?.message?.content || content;\n        } catch (error) {\n            console.error('OpenAI API error:', error);\n            return this.fallbackSummarize(content);\n        }\n    }\n    async analyze(content, context) {\n        // Advanced analysis using AI\n        const wordCount = content.split(/\\s+/).length;\n        const estimatedReadingTime = Math.ceil(wordCount / 200); // 200 WPM average\n        if (!this.apiKey || this.apiKey === 'your_openai_api_key_here') {\n            return this.fallbackAnalyze(content);\n        }\n        try {\n            const response = await fetch('https://api.openai.com/v1/chat/completions', {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${this.apiKey}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: 'gpt-4',\n                    messages: [\n                        {\n                            role: 'system',\n                            content: 'You are a text analysis expert. Analyze the given text and return a JSON object with readabilityScore (0-100), sentiment (positive/neutral/negative), keyTopics (array of strings), and suggestions (array of strings).'\n                        },\n                        {\n                            role: 'user',\n                            content: `Analyze this text: ${content}`\n                        }\n                    ],\n                    max_tokens: 800,\n                    temperature: 0.1\n                })\n            });\n            const data = await response.json();\n            const analysis = JSON.parse(data.choices[0]?.message?.content || '{}');\n            return {\n                readabilityScore: analysis.readabilityScore || 75,\n                sentiment: analysis.sentiment || 'neutral',\n                keyTopics: analysis.keyTopics || [\n                    'general'\n                ],\n                suggestions: analysis.suggestions || [\n                    'Consider adding more detail'\n                ],\n                wordCount,\n                estimatedReadingTime\n            };\n        } catch (error) {\n            console.error('OpenAI API error:', error);\n            return this.fallbackAnalyze(content);\n        }\n    }\n    async chat(message, history) {\n        if (!this.apiKey || this.apiKey === 'your_openai_api_key_here') {\n            return this.fallbackChat(message);\n        }\n        try {\n            const messages = [\n                {\n                    role: 'system',\n                    content: 'You are a helpful writing assistant. Provide clear, actionable advice to help users improve their writing. Be encouraging and specific in your suggestions.'\n                },\n                ...history?.slice(-5).map((h)=>({\n                        role: h.role,\n                        content: h.content\n                    })) || [],\n                {\n                    role: 'user',\n                    content: message\n                }\n            ];\n            const response = await fetch('https://api.openai.com/v1/chat/completions', {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${this.apiKey}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: 'gpt-4',\n                    messages,\n                    max_tokens: 500,\n                    temperature: 0.8\n                })\n            });\n            const data = await response.json();\n            return data.choices[0]?.message?.content || 'I apologize, but I encountered an error. Please try again.';\n        } catch (error) {\n            console.error('OpenAI API error:', error);\n            return this.fallbackChat(message);\n        }\n    }\n    // Fallback methods for when API is not available\n    fallbackEnhance(content) {\n        return content.replace(/\\b(good|nice|great)\\b/gi, (match)=>{\n            const alternatives = {\n                'good': 'excellent',\n                'nice': 'remarkable',\n                'great': 'outstanding'\n            };\n            return alternatives[match.toLowerCase()] || match;\n        }).replace(/\\. ([A-Z])/g, '. \\n\\n$1');\n    }\n    fallbackSummarize(content) {\n        const sentences = content.split(/[.!?]+/).filter((s)=>s.trim().length > 0);\n        const keyPoints = sentences.slice(0, Math.max(3, Math.floor(sentences.length / 3)));\n        return `# Summary\\n\\n${keyPoints.map((point, i)=>`${i + 1}. ${point.trim()}.`).join('\\n')}`;\n    }\n    fallbackAnalyze(content) {\n        const wordCount = content.split(/\\s+/).length;\n        return {\n            readabilityScore: 75,\n            sentiment: 'neutral',\n            keyTopics: [\n                'general'\n            ],\n            suggestions: [\n                'Consider adding more detail',\n                'Review for clarity'\n            ],\n            wordCount,\n            estimatedReadingTime: Math.ceil(wordCount / 200)\n        };\n    }\n    fallbackChat(message) {\n        const responses = [\n            \"I'm here to help with your writing. What would you like to work on?\",\n            \"Let's craft something amazing together. What's on your mind?\",\n            \"I can assist with writing, editing, brainstorming, or structuring. How can I help?\"\n        ];\n        return responses[Math.floor(Math.random() * responses.length)];\n    }\n}\n// AI Service Factory\nclass AIService {\n    constructor(){\n        // Initialize with OpenAI by default\n        const apiKey = process.env.OPENAI_API_KEY || '';\n        this.provider = new OpenAIProvider(apiKey);\n    }\n    static getInstance() {\n        if (!AIService.instance) {\n            AIService.instance = new AIService();\n        }\n        return AIService.instance;\n    }\n    async enhance(content, context) {\n        return this.provider.enhance(content, context);\n    }\n    async summarize(content, context) {\n        return this.provider.summarize(content, context);\n    }\n    async analyze(content, context) {\n        return this.provider.analyze(content, context);\n    }\n    async chat(message, history) {\n        return this.provider.chat(message, history);\n    }\n    getProviderName() {\n        return this.provider.name;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ai-service.ts\n");

/***/ }),

/***/ "(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*******************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*******************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();