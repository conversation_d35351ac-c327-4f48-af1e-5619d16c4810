/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?3725\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FThemeSelector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FThemeSelector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AnalyticsDashboard.tsx */ \"(rsc)/./src/components/AnalyticsDashboard.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScribeChatPanel.tsx */ \"(rsc)/./src/components/ScribeChatPanel.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScrollEditor.tsx */ \"(rsc)/./src/components/ScrollEditor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/TemplateSidebar.tsx */ \"(rsc)/./src/components/TemplateSidebar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeSelector.tsx */ \"(rsc)/./src/components/ThemeSelector.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FThemeSelector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vLi4vLm5wbS9fbnB4LzkwZWI3Yzc4Mjg2Y2MwNDMvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXIuanM/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIS4vYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9ob21lL2dob3N0LWluLXRoZS13aXJlL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL3dyaXRlb3Mtc2NyaWJlLXRlcm1pbmFsL3dyaXRlb3Mtc2NyaWJlLXRlcm1pbmFsL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9ob21lL2dob3N0LWluLXRoZS13aXJlL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL3dyaXRlb3Mtc2NyaWJlLXRlcm1pbmFsL3dyaXRlb3Mtc2NyaWJlLXRlcm1pbmFsL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"writeOS-scribe-terminal\",\n    description: \"Sovereign scroll editor + AI assistant app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"bg-zinc-950 text-white min-h-screen\",\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDdUI7QUFFaEIsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBVTtzQkFDYko7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9ob21lL2dob3N0LWluLXRoZS13aXJlL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL3dyaXRlb3Mtc2NyaWJlLXRlcm1pbmFsL3dyaXRlb3Mtc2NyaWJlLXRlcm1pbmFsL2FwcC9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJ3cml0ZU9TLXNjcmliZS10ZXJtaW5hbFwiLFxuICBkZXNjcmlwdGlvbjogXCJTb3ZlcmVpZ24gc2Nyb2xsIGVkaXRvciArIEFJIGFzc2lzdGFudCBhcHBcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9XCJiZy16aW5jLTk1MCB0ZXh0LXdoaXRlIG1pbi1oLXNjcmVlblwiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_components_TemplateSidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../src/components/TemplateSidebar */ \"(rsc)/./src/components/TemplateSidebar.tsx\");\n/* harmony import */ var _src_components_ScrollEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../src/components/ScrollEditor */ \"(rsc)/./src/components/ScrollEditor.tsx\");\n/* harmony import */ var _src_components_ScribeChatPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../src/components/ScribeChatPanel */ \"(rsc)/./src/components/ScribeChatPanel.tsx\");\n/* harmony import */ var _src_components_AnalyticsDashboard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../src/components/AnalyticsDashboard */ \"(rsc)/./src/components/AnalyticsDashboard.tsx\");\n/* harmony import */ var _src_components_ThemeSelector__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../src/components/ThemeSelector */ \"(rsc)/./src/components/ThemeSelector.tsx\");\n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"grid grid-cols-[260px_1fr_1fr] h-screen w-full overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-r border-zinc-800 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_TemplateSidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-r border-zinc-800 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ScrollEditor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ScribeChatPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ThemeSelector__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_AnalyticsDashboard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/AnalyticsDashboard.tsx":
/*!***********************************************!*\
  !*** ./src/components/AnalyticsDashboard.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ScribeChatPanel.tsx":
/*!********************************************!*\
  !*** ./src/components/ScribeChatPanel.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ScrollEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/ScrollEditor.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/TemplateSidebar.tsx":
/*!********************************************!*\
  !*** ./src/components/TemplateSidebar.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ThemeSelector.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeSelector.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FThemeSelector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FThemeSelector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AnalyticsDashboard.tsx */ \"(ssr)/./src/components/AnalyticsDashboard.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScribeChatPanel.tsx */ \"(ssr)/./src/components/ScribeChatPanel.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScrollEditor.tsx */ \"(ssr)/./src/components/ScrollEditor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/TemplateSidebar.tsx */ \"(ssr)/./src/components/TemplateSidebar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeSelector.tsx */ \"(ssr)/./src/components/ThemeSelector.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FThemeSelector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/AnalyticsDashboard.tsx":
/*!***********************************************!*\
  !*** ./src/components/AnalyticsDashboard.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AnalyticsDashboard() {\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentSession, setCurrentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsDashboard.useEffect\": ()=>{\n            // Load analytics data from localStorage\n            loadAnalyticsData();\n            // Start tracking current session\n            startSession();\n            // Update session every minute\n            const interval = setInterval(updateCurrentSession, 60000);\n            return ({\n                \"AnalyticsDashboard.useEffect\": ()=>{\n                    clearInterval(interval);\n                    endSession();\n                }\n            })[\"AnalyticsDashboard.useEffect\"];\n        }\n    }[\"AnalyticsDashboard.useEffect\"], []);\n    const loadAnalyticsData = ()=>{\n        try {\n            const stored = localStorage.getItem('writeos-analytics');\n            if (stored) {\n                setAnalyticsData(JSON.parse(stored));\n            } else {\n                // Initialize with default data\n                const defaultData = {\n                    totalSessions: 0,\n                    totalWordsWritten: 0,\n                    totalTimeSpent: 0,\n                    averageWordsPerSession: 0,\n                    averageSessionLength: 0,\n                    mostProductiveHour: 14,\n                    weeklyProgress: [\n                        0,\n                        0,\n                        0,\n                        0,\n                        0,\n                        0,\n                        0\n                    ],\n                    aiUsageStats: {\n                        enhanceCount: 0,\n                        summarizeCount: 0,\n                        analyzeCount: 0,\n                        chatMessages: 0\n                    }\n                };\n                setAnalyticsData(defaultData);\n                localStorage.setItem('writeos-analytics', JSON.stringify(defaultData));\n            }\n        } catch (error) {\n            console.error('Error loading analytics data:', error);\n        }\n    };\n    const startSession = ()=>{\n        const session = {\n            id: Date.now().toString(),\n            startTime: new Date(),\n            wordCount: 0,\n            charactersTyped: 0,\n            timeSpent: 0,\n            documentsCreated: 0,\n            aiInteractions: 0\n        };\n        setCurrentSession(session);\n    };\n    const updateCurrentSession = ()=>{\n        if (currentSession) {\n            const now = new Date();\n            const timeSpent = Math.floor((now.getTime() - currentSession.startTime.getTime()) / 60000);\n            setCurrentSession((prev)=>prev ? {\n                    ...prev,\n                    timeSpent\n                } : null);\n        }\n    };\n    const endSession = ()=>{\n        if (currentSession && analyticsData) {\n            const updatedData = {\n                ...analyticsData,\n                totalSessions: analyticsData.totalSessions + 1,\n                totalTimeSpent: analyticsData.totalTimeSpent + currentSession.timeSpent,\n                totalWordsWritten: analyticsData.totalWordsWritten + currentSession.wordCount,\n                averageWordsPerSession: Math.round((analyticsData.totalWordsWritten + currentSession.wordCount) / (analyticsData.totalSessions + 1)),\n                averageSessionLength: Math.round((analyticsData.totalTimeSpent + currentSession.timeSpent) / (analyticsData.totalSessions + 1))\n            };\n            setAnalyticsData(updatedData);\n            localStorage.setItem('writeos-analytics', JSON.stringify(updatedData));\n        }\n    };\n    const formatTime = (minutes)=>{\n        const hours = Math.floor(minutes / 60);\n        const mins = minutes % 60;\n        return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n    };\n    const getProductivityLevel = ()=>{\n        if (!currentSession) return 'Starting';\n        if (currentSession.timeSpent < 5) return 'Warming up';\n        if (currentSession.timeSpent < 15) return 'Getting focused';\n        if (currentSession.timeSpent < 30) return 'In the zone';\n        if (currentSession.timeSpent < 60) return 'Deep work';\n        return 'Marathon session!';\n    };\n    if (!analyticsData) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsVisible(!isVisible),\n                className: \"bg-flame hover:bg-flame/80 text-white p-3 rounded-full shadow-lg transition-all duration-300 mb-2\",\n                children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-zinc-900 border border-zinc-700 rounded-lg p-4 w-80 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-flame font-bold text-lg\",\n                                children: \"\\uD83D\\uDCCA Writing Analytics\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsVisible(false),\n                                className: \"text-zinc-400 hover:text-white\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-3 bg-zinc-800 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-ghostblue font-semibold mb-2\",\n                                children: \"Current Session\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-zinc-400\",\n                                                children: \"Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: getProductivityLevel()\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-zinc-400\",\n                                                children: \"Time:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: formatTime(currentSession?.timeSpent || 0)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-zinc-400\",\n                                                children: \"Words:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: currentSession?.wordCount || 0\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-zinc-800 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-flame\",\n                                                children: analyticsData.totalSessions\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-zinc-400\",\n                                                children: \"Total Sessions\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-zinc-800 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-ghostblue\",\n                                                children: analyticsData.totalWordsWritten.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-zinc-400\",\n                                                children: \"Words Written\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-zinc-800 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: formatTime(analyticsData.totalTimeSpent)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-zinc-400\",\n                                                children: \"Time Spent\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-zinc-800 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: analyticsData.averageWordsPerSession\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-zinc-400\",\n                                                children: \"Avg Words/Session\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-zinc-800 p-3 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"text-ghostblue font-semibold mb-2 text-sm\",\n                                        children: \"AI Assistance\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Enhance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: analyticsData.aiUsageStats.enhanceCount\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Analyze:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: analyticsData.aiUsageStats.analyzeCount\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Summarize:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: analyticsData.aiUsageStats.summarizeCount\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Chat:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: analyticsData.aiUsageStats.chatMessages\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-zinc-800 p-3 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"text-flame font-semibold mb-2 text-sm\",\n                                        children: \"\\uD83D\\uDCA1 Insights\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-zinc-300 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• Most productive at \",\n                                                    analyticsData.mostProductiveHour,\n                                                    \":00\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• Average session: \",\n                                                    formatTime(analyticsData.averageSessionLength)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• \",\n                                                    analyticsData.totalWordsWritten > 1000 ? 'Great progress!' : 'Keep writing!'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AnalyticsDashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ScribeChatPanel.tsx":
/*!********************************************!*\
  !*** ./src/components/ScribeChatPanel.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScribeChatPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ScribeChatPanel() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            role: 'assistant',\n            content: 'Welcome to your AI writing assistant. How can I help you craft your scroll today?',\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScribeChatPanel.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ScribeChatPanel.useEffect\"], [\n        messages\n    ]);\n    const sendMessage = async ()=>{\n        if (!input.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: input.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput('');\n        setIsLoading(true);\n        try {\n            // Phase III - Real AI Integration\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    history: messages.slice(-5),\n                    context: 'writing-assistant'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                const aiResponse = {\n                    id: (Date.now() + 1).toString(),\n                    role: 'assistant',\n                    content: result.message,\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiResponse\n                    ]);\n                // Update analytics\n                updateAnalytics('chatMessage');\n            } else {\n                // Fallback to local response\n                const aiResponse = {\n                    id: (Date.now() + 1).toString(),\n                    role: 'assistant',\n                    content: generateAIResponse(userMessage.content),\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiResponse\n                    ]);\n            }\n        } catch (error) {\n            console.error('Error sending message:', error);\n            // Fallback response\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                role: 'assistant',\n                content: \"I apologize, but I'm having trouble connecting right now. Please try again in a moment.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiResponse\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateAnalytics = (action)=>{\n        try {\n            const stored = localStorage.getItem('writeos-analytics');\n            if (stored) {\n                const analytics = JSON.parse(stored);\n                if (action === 'chatMessage') {\n                    analytics.aiUsageStats.chatMessages += 1;\n                }\n                localStorage.setItem('writeos-analytics', JSON.stringify(analytics));\n            }\n        } catch (error) {\n            console.error('Error updating analytics:', error);\n        }\n    };\n    const generateAIResponse = (userInput)=>{\n        // Enhanced AI response logic\n        const responses = {\n            writing: [\n                \"I can help you structure your thoughts. What type of document are you working on?\",\n                \"Let's break this down into clear sections. What's your main objective?\",\n                \"Consider starting with an outline. What are the key points you want to cover?\"\n            ],\n            editing: [\n                \"I can help refine your prose. Share a paragraph and I'll suggest improvements.\",\n                \"Let's focus on clarity and flow. What section needs the most work?\",\n                \"Consider your audience - who are you writing for?\"\n            ],\n            ideas: [\n                \"Let's brainstorm! What's the core concept you want to explore?\",\n                \"I can help generate creative angles. What's your starting point?\",\n                \"What if we approached this from a different perspective?\"\n            ],\n            default: [\n                \"I'm here to help with your writing. What would you like to work on?\",\n                \"Let's craft something amazing together. What's on your mind?\",\n                \"I can assist with writing, editing, brainstorming, or structuring. How can I help?\"\n            ]\n        };\n        const input = userInput.toLowerCase();\n        if (input.includes('write') || input.includes('draft')) {\n            return responses.writing[Math.floor(Math.random() * responses.writing.length)];\n        } else if (input.includes('edit') || input.includes('improve') || input.includes('fix')) {\n            return responses.editing[Math.floor(Math.random() * responses.editing.length)];\n        } else if (input.includes('idea') || input.includes('brainstorm') || input.includes('think')) {\n            return responses.ideas[Math.floor(Math.random() * responses.ideas.length)];\n        } else {\n            return responses.default[Math.floor(Math.random() * responses.default.length)];\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-flame text-lg font-bold mb-4\",\n                children: \"\\uD83E\\uDD16 Scribe Assistant\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg p-4 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto mb-4 space-y-3\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `max-w-[80%] p-3 rounded-lg text-sm ${message.role === 'user' ? 'bg-flame text-white' : 'bg-zinc-700 text-zinc-100'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-60 mt-1\",\n                                                children: message.timestamp.toLocaleTimeString([], {\n                                                    hour: '2-digit',\n                                                    minute: '2-digit'\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                }, message.id, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-zinc-700 text-zinc-100 p-3 rounded-lg text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Thinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                className: \"flex-1 bg-zinc-800 border border-zinc-700 rounded px-3 py-2 text-white text-sm focus:outline-none focus:border-ghostblue\",\n                                placeholder: \"Ask your scribe assistant...\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: sendMessage,\n                                disabled: !input.trim() || isLoading,\n                                className: \"bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded text-sm font-medium transition-colors\",\n                                children: \"Send\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ScribeChatPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ScrollEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/ScrollEditor.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ScrollEditor() {\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isPreview, setIsPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            if (content.trim()) {\n                const timer = setTimeout({\n                    \"ScrollEditor.useEffect.timer\": ()=>{\n                        // Simulate auto-save\n                        localStorage.setItem('scroll-content', content);\n                        setLastSaved(new Date());\n                    }\n                }[\"ScrollEditor.useEffect.timer\"], 2000);\n                return ({\n                    \"ScrollEditor.useEffect\": ()=>clearTimeout(timer)\n                })[\"ScrollEditor.useEffect\"];\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], [\n        content\n    ]);\n    // Load saved content on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const saved = localStorage.getItem('scroll-content');\n            if (saved) {\n                setContent(saved);\n                setLastSaved(new Date());\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Listen for template loading events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const handleLoadTemplate = {\n                \"ScrollEditor.useEffect.handleLoadTemplate\": (event)=>{\n                    const template = event.detail;\n                    setContent(template.content);\n                    setLastSaved(null); // Reset save status for new template\n                    // Focus the editor\n                    setTimeout({\n                        \"ScrollEditor.useEffect.handleLoadTemplate\": ()=>{\n                            textareaRef.current?.focus();\n                        }\n                    }[\"ScrollEditor.useEffect.handleLoadTemplate\"], 100);\n                }\n            }[\"ScrollEditor.useEffect.handleLoadTemplate\"];\n            window.addEventListener('loadTemplate', handleLoadTemplate);\n            return ({\n                \"ScrollEditor.useEffect\": ()=>{\n                    window.removeEventListener('loadTemplate', handleLoadTemplate);\n                }\n            })[\"ScrollEditor.useEffect\"];\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Calculate stats\n    const wordCount = content.trim() ? content.trim().split(/\\s+/).length : 0;\n    const charCount = content.length;\n    const lineCount = content.split('\\n').length;\n    // Simple markdown preview (basic implementation)\n    const renderMarkdown = (text)=>{\n        return text.replace(/^# (.*$)/gim, '<h1 class=\"text-2xl font-bold text-ghostblue mb-4\">$1</h1>').replace(/^## (.*$)/gim, '<h2 class=\"text-xl font-bold text-white mb-3\">$1</h2>').replace(/^### (.*$)/gim, '<h3 class=\"text-lg font-bold text-zinc-300 mb-2\">$1</h3>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong class=\"font-bold text-white\">$1</strong>').replace(/\\*(.*?)\\*/g, '<em class=\"italic text-zinc-300\">$1</em>').replace(/`(.*?)`/g, '<code class=\"bg-zinc-800 text-flame px-1 rounded\">$1</code>').replace(/\\n/g, '<br>');\n    };\n    const handleKeyDown = (e)=>{\n        // Tab support\n        if (e.key === 'Tab') {\n            e.preventDefault();\n            const start = e.currentTarget.selectionStart;\n            const end = e.currentTarget.selectionEnd;\n            const newContent = content.substring(0, start) + '  ' + content.substring(end);\n            setContent(newContent);\n            // Restore cursor position\n            setTimeout(()=>{\n                if (textareaRef.current) {\n                    textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 2;\n                }\n            }, 0);\n        }\n    };\n    const insertTemplate = (template)=>{\n        const templates = {\n            heading: '# Your Heading Here\\n\\n',\n            list: '- Item 1\\n- Item 2\\n- Item 3\\n\\n',\n            code: '```\\nYour code here\\n```\\n\\n',\n            quote: '> Your quote here\\n\\n',\n            table: '| Column 1 | Column 2 |\\n|----------|----------|\\n| Cell 1   | Cell 2   |\\n\\n'\n        };\n        const insertion = templates[template] || '';\n        const textarea = textareaRef.current;\n        if (textarea) {\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const newContent = content.substring(0, start) + insertion + content.substring(end);\n            setContent(newContent);\n            // Focus and position cursor\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.selectionStart = textarea.selectionEnd = start + insertion.length;\n            }, 0);\n        }\n    };\n    const handleAscend = async (action)=>{\n        if (!content.trim() || isProcessing) return;\n        setIsProcessing(true);\n        try {\n            const response = await fetch('/api/ascend', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content,\n                    action,\n                    context: 'scroll-editor'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setContent(result.processedContent);\n                setLastSaved(null); // Reset save status\n                // Update analytics\n                updateAnalytics(action);\n                // Show success notification with AI provider info\n                console.log('Ascend completed:', result.message);\n                console.log('AI Provider:', result.aiProvider || 'Fallback System');\n                console.log('Processing Time:', result.metadata?.processingTime || 'N/A');\n                console.log('Suggestions:', result.suggestions);\n                // You could add a toast notification here\n                showNotification(`${action} completed successfully!`, 'success');\n            } else {\n                console.error('Ascend failed:', result.error);\n                showNotification(`${action} failed. Please try again.`, 'error');\n            }\n        } catch (error) {\n            console.error('Ascend error:', error);\n            showNotification('Connection error. Please check your internet.', 'error');\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    const updateAnalytics = (action)=>{\n        try {\n            const stored = localStorage.getItem('writeos-analytics');\n            if (stored) {\n                const analytics = JSON.parse(stored);\n                switch(action){\n                    case 'enhance':\n                        analytics.aiUsageStats.enhanceCount += 1;\n                        break;\n                    case 'summarize':\n                        analytics.aiUsageStats.summarizeCount += 1;\n                        break;\n                    case 'analyze':\n                        analytics.aiUsageStats.analyzeCount += 1;\n                        break;\n                }\n                localStorage.setItem('writeos-analytics', JSON.stringify(analytics));\n            }\n        } catch (error) {\n            console.error('Error updating analytics:', error);\n        }\n    };\n    const showNotification = (message, type)=>{\n        // Simple notification system - you could enhance this with a proper toast library\n        console.log(`[${type.toUpperCase()}] ${message}`);\n        // Create a temporary visual notification\n        const notification = document.createElement('div');\n        notification.className = `fixed top-4 right-4 p-3 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;\n        notification.textContent = message;\n        document.body.appendChild(notification);\n        setTimeout(()=>{\n            document.body.removeChild(notification);\n        }, 3000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-ghostblue text-lg font-bold\",\n                        children: \"✍️ Scroll Editor\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsPreview(!isPreview),\n                                className: `px-3 py-1 rounded text-xs font-medium transition-colors ${isPreview ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-700 text-white hover:bg-zinc-600'}`,\n                                children: isPreview ? '📝 Edit' : '👁️ Preview'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    '🔥 Enhance',\n                                    '📋 Summarize',\n                                    '🎨 Format',\n                                    '📊 Analyze'\n                                ].map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleAscend(action.split(' ')[1].toLowerCase()),\n                                        className: \"px-2 py-1 bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white text-xs rounded font-medium transition-colors\",\n                                        disabled: !content.trim() || isProcessing,\n                                        children: isProcessing ? '⏳' : action\n                                    }, action, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-zinc-500\",\n                                children: lastSaved ? `Saved ${lastSaved.toLocaleTimeString()}` : 'Not saved'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-1 mb-3\",\n                children: [\n                    {\n                        label: 'H1',\n                        action: ()=>insertTemplate('heading')\n                    },\n                    {\n                        label: 'List',\n                        action: ()=>insertTemplate('list')\n                    },\n                    {\n                        label: 'Code',\n                        action: ()=>insertTemplate('code')\n                    },\n                    {\n                        label: 'Quote',\n                        action: ()=>insertTemplate('quote')\n                    },\n                    {\n                        label: 'Table',\n                        action: ()=>insertTemplate('table')\n                    }\n                ].map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: tool.action,\n                        className: \"px-2 py-1 bg-zinc-800 hover:bg-zinc-700 text-zinc-300 text-xs rounded transition-colors\",\n                        children: tool.label\n                    }, tool.label, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg overflow-hidden\",\n                children: isPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-4 overflow-y-auto prose prose-invert max-w-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white leading-relaxed\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(content)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: content,\n                    onChange: (e)=>setContent(e.target.value),\n                    onKeyDown: handleKeyDown,\n                    className: \"w-full h-full bg-transparent text-white resize-none outline-none p-4 font-mono text-sm leading-relaxed\",\n                    placeholder: \"Begin your scroll here...  # Welcome to your Scroll Editor  Start writing in **Markdown** format: - Use # for headings - Use **bold** and *italic* text - Create `code snippets` - Make lists and more!  Press Tab for indentation, Ctrl+Enter for preview.\",\n                    spellCheck: false\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 flex items-center justify-between text-xs text-zinc-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Words: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: wordCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Characters: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: charCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Lines: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: lineCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            content.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-ghostblue rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Ready\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ScrollEditor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TemplateSidebar.tsx":
/*!********************************************!*\
  !*** ./src/components/TemplateSidebar.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TemplateSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction TemplateSidebar() {\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const templates = [\n        {\n            id: '1',\n            name: \"Blank Scroll\",\n            icon: \"📜\",\n            content: \"# New Document\\n\\nStart writing here...\",\n            description: \"Empty document to start fresh\",\n            category: 'writing'\n        },\n        {\n            id: '2',\n            name: \"Technical Doc\",\n            icon: \"⚙️\",\n            content: \"# Technical Documentation\\n\\n## Overview\\n\\n## Requirements\\n\\n## Implementation\\n\\n## Testing\\n\\n## Deployment\",\n            description: \"Structure for technical documentation\",\n            category: 'technical'\n        },\n        {\n            id: '3',\n            name: \"Creative Writing\",\n            icon: \"✨\",\n            content: \"# Story Title\\n\\n*Genre: [Your Genre]*\\n\\n## Characters\\n\\n## Plot Outline\\n\\n## Chapter 1\\n\\nOnce upon a time...\",\n            description: \"Template for creative stories\",\n            category: 'creative'\n        },\n        {\n            id: '4',\n            name: \"Meeting Notes\",\n            icon: \"📝\",\n            content: \"# Meeting Notes\\n\\n**Date:** [Date]\\n**Attendees:** [Names]\\n**Agenda:**\\n\\n## Discussion Points\\n\\n## Action Items\\n\\n## Next Steps\",\n            description: \"Structured meeting documentation\",\n            category: 'business'\n        },\n        {\n            id: '5',\n            name: \"Project Plan\",\n            icon: \"🎯\",\n            content: \"# Project Plan\\n\\n## Objective\\n\\n## Scope\\n\\n## Timeline\\n\\n## Resources\\n\\n## Milestones\\n\\n## Risk Assessment\",\n            description: \"Comprehensive project planning\",\n            category: 'business'\n        },\n        {\n            id: '6',\n            name: \"Blog Post\",\n            icon: \"📰\",\n            content: \"# Blog Post Title\\n\\n*Published: [Date]*\\n*Tags: [tag1, tag2]*\\n\\n## Introduction\\n\\n## Main Content\\n\\n## Conclusion\\n\\n---\\n*What do you think? Leave a comment below!*\",\n            description: \"Blog post structure\",\n            category: 'writing'\n        },\n        {\n            id: '7',\n            name: \"API Documentation\",\n            icon: \"🔌\",\n            content: \"# API Documentation\\n\\n## Endpoints\\n\\n### GET /api/endpoint\\n\\n**Description:** \\n\\n**Parameters:**\\n\\n**Response:**\\n\\n```json\\n{\\n  \\\"status\\\": \\\"success\\\"\\n}\\n```\",\n            description: \"API reference template\",\n            category: 'technical'\n        }\n    ];\n    const categories = [\n        {\n            id: 'all',\n            name: 'All',\n            icon: '📚'\n        },\n        {\n            id: 'writing',\n            name: 'Writing',\n            icon: '✍️'\n        },\n        {\n            id: 'business',\n            name: 'Business',\n            icon: '💼'\n        },\n        {\n            id: 'technical',\n            name: 'Technical',\n            icon: '⚙️'\n        },\n        {\n            id: 'creative',\n            name: 'Creative',\n            icon: '🎨'\n        }\n    ];\n    const filteredTemplates = selectedCategory === 'all' ? templates : templates.filter((t)=>t.category === selectedCategory);\n    const loadTemplate = (template)=>{\n        // Store the template content to be loaded by the editor\n        localStorage.setItem('scroll-content', template.content);\n        // Trigger a custom event to notify the editor\n        window.dispatchEvent(new CustomEvent('loadTemplate', {\n            detail: template\n        }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-flame text-lg font-bold mb-4\",\n                children: \"\\uD83D\\uDCDC Templates\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-zinc-400 mb-2\",\n                        children: \"Categories\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-1\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedCategory(category.id),\n                                className: `px-2 py-1 rounded text-xs transition-colors ${selectedCategory === category.id ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'}`,\n                                children: [\n                                    category.icon,\n                                    \" \",\n                                    category.name\n                                ]\n                            }, category.id, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 space-y-2 overflow-y-auto\",\n                children: filteredTemplates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadTemplate(template),\n                        className: \"w-full text-left p-3 rounded-lg bg-zinc-800 hover:bg-zinc-700 border border-zinc-700 hover:border-zinc-600 transition-colors group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg flex-shrink-0\",\n                                    children: template.icon\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white text-sm font-medium group-hover:text-ghostblue transition-colors\",\n                                            children: template.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-zinc-400 text-xs mt-1 line-clamp-2\",\n                                            children: template.description\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this)\n                    }, template.id, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-4 border-t border-zinc-700 space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full bg-ghostblue hover:bg-ghostblue/80 text-zinc-900 py-2 px-4 rounded font-medium text-sm transition-colors\",\n                        children: \"+ New Template\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full bg-zinc-800 hover:bg-zinc-700 text-white py-2 px-4 rounded font-medium text-sm transition-colors\",\n                        children: \"\\uD83D\\uDCC1 Browse Scrolls\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TemplateSidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeSelector.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeSelector.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst themes = [\n    {\n        id: 'flame-empire',\n        name: 'Flame Empire',\n        description: 'The original sovereign theme',\n        colors: {\n            primary: '#FF6B00',\n            secondary: '#2DD4BF',\n            background: '#0D0D1A',\n            surface: '#1E1B24',\n            text: '#FFFFFF',\n            accent: '#FF6B00'\n        },\n        icon: '🔥'\n    },\n    {\n        id: 'midnight-scholar',\n        name: 'Midnight Scholar',\n        description: 'Deep blues for focused writing',\n        colors: {\n            primary: '#3B82F6',\n            secondary: '#8B5CF6',\n            background: '#0F172A',\n            surface: '#1E293B',\n            text: '#F8FAFC',\n            accent: '#60A5FA'\n        },\n        icon: '🌙'\n    },\n    {\n        id: 'forest-sage',\n        name: 'Forest Sage',\n        description: 'Natural greens for calm creativity',\n        colors: {\n            primary: '#10B981',\n            secondary: '#34D399',\n            background: '#064E3B',\n            surface: '#065F46',\n            text: '#ECFDF5',\n            accent: '#6EE7B7'\n        },\n        icon: '🌲'\n    },\n    {\n        id: 'royal-purple',\n        name: 'Royal Purple',\n        description: 'Majestic purples for elegant writing',\n        colors: {\n            primary: '#8B5CF6',\n            secondary: '#A78BFA',\n            background: '#1E1B4B',\n            surface: '#312E81',\n            text: '#F3F4F6',\n            accent: '#C4B5FD'\n        },\n        icon: '👑'\n    },\n    {\n        id: 'sunset-writer',\n        name: 'Sunset Writer',\n        description: 'Warm oranges and pinks',\n        colors: {\n            primary: '#F59E0B',\n            secondary: '#EC4899',\n            background: '#451A03',\n            surface: '#92400E',\n            text: '#FEF3C7',\n            accent: '#FBBF24'\n        },\n        icon: '🌅'\n    }\n];\nfunction ThemeSelector() {\n    const [currentTheme, setCurrentTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('flame-empire');\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeSelector.useEffect\": ()=>{\n            // Load saved theme\n            const savedTheme = localStorage.getItem('writeos-theme');\n            if (savedTheme) {\n                setCurrentTheme(savedTheme);\n                applyTheme(savedTheme);\n            }\n        }\n    }[\"ThemeSelector.useEffect\"], []);\n    const applyTheme = (themeId)=>{\n        const theme = themes.find((t)=>t.id === themeId);\n        if (!theme) return;\n        const root = document.documentElement;\n        // Apply CSS custom properties\n        root.style.setProperty('--color-primary', theme.colors.primary);\n        root.style.setProperty('--color-secondary', theme.colors.secondary);\n        root.style.setProperty('--color-background', theme.colors.background);\n        root.style.setProperty('--color-surface', theme.colors.surface);\n        root.style.setProperty('--color-text', theme.colors.text);\n        root.style.setProperty('--color-accent', theme.colors.accent);\n        // Update Tailwind classes dynamically\n        const style = document.createElement('style');\n        style.innerHTML = `\n      :root {\n        --flame: ${theme.colors.primary};\n        --ghostblue: ${theme.colors.secondary};\n        --scrollbg: ${theme.colors.background};\n        --shadowline: ${theme.colors.surface};\n      }\n      \n      .bg-flame { background-color: ${theme.colors.primary} !important; }\n      .text-flame { color: ${theme.colors.primary} !important; }\n      .bg-ghostblue { background-color: ${theme.colors.secondary} !important; }\n      .text-ghostblue { color: ${theme.colors.secondary} !important; }\n      .bg-scrollbg { background-color: ${theme.colors.background} !important; }\n      .border-shadowline { border-color: ${theme.colors.surface} !important; }\n    `;\n        // Remove old theme styles\n        const oldStyle = document.getElementById('theme-styles');\n        if (oldStyle) {\n            oldStyle.remove();\n        }\n        style.id = 'theme-styles';\n        document.head.appendChild(style);\n    };\n    const selectTheme = (themeId)=>{\n        setCurrentTheme(themeId);\n        applyTheme(themeId);\n        localStorage.setItem('writeos-theme', themeId);\n        setIsOpen(false);\n    };\n    const currentThemeData = themes.find((t)=>t.id === currentTheme);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 left-4 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"bg-zinc-800 hover:bg-zinc-700 border border-zinc-600 text-white p-2 rounded-lg shadow-lg transition-all duration-300 flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg\",\n                        children: currentThemeData?.icon\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium hidden sm:block\",\n                        children: currentThemeData?.name\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-12 left-0 bg-zinc-900 border border-zinc-700 rounded-lg p-4 w-80 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-flame font-bold text-lg\",\n                                children: \"\\uD83C\\uDFA8 Theme Selector\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(false),\n                                className: \"text-zinc-400 hover:text-white\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: themes.map((theme)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>selectTheme(theme.id),\n                                className: `w-full text-left p-3 rounded-lg border transition-all duration-200 ${currentTheme === theme.id ? 'border-flame bg-zinc-800' : 'border-zinc-700 bg-zinc-800 hover:bg-zinc-700 hover:border-zinc-600'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: theme.icon\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-white mb-1\",\n                                                    children: theme.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-zinc-400 mb-2\",\n                                                    children: theme.description\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 rounded-full border border-zinc-600\",\n                                                            style: {\n                                                                backgroundColor: theme.colors.primary\n                                                            },\n                                                            title: \"Primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 rounded-full border border-zinc-600\",\n                                                            style: {\n                                                                backgroundColor: theme.colors.secondary\n                                                            },\n                                                            title: \"Secondary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 rounded-full border border-zinc-600\",\n                                                            style: {\n                                                                backgroundColor: theme.colors.background\n                                                            },\n                                                            title: \"Background\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 rounded-full border border-zinc-600\",\n                                                            style: {\n                                                                backgroundColor: theme.colors.surface\n                                                            },\n                                                            title: \"Surface\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentTheme === theme.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-flame text-sm\",\n                                            children: \"✓\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, this)\n                            }, theme.id, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 pt-4 border-t border-zinc-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-zinc-400\",\n                            children: \"\\uD83D\\uDCA1 Themes are automatically saved and will persist across sessions\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeSelector.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();