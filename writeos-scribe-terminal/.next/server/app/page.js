/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?3725\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScribeChatPanel.tsx */ \"(rsc)/./src/components/ScribeChatPanel.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScrollEditor.tsx */ \"(rsc)/./src/components/ScrollEditor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/TemplateSidebar.tsx */ \"(rsc)/./src/components/TemplateSidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vLi4vLi4vLm5wbS9fbnB4LzkwZWI3Yzc4Mjg2Y2MwNDMvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1tZXRhZGF0YS1pbWFnZS1sb2FkZXIuanM/dHlwZT1mYXZpY29uJnNlZ21lbnQ9JmJhc2VQYXRoPSZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzIS4vYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9ob21lL2dob3N0LWluLXRoZS13aXJlL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL3dyaXRlb3Mtc2NyaWJlLXRlcm1pbmFsL3dyaXRlb3Mtc2NyaWJlLXRlcm1pbmFsL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9ob21lL2dob3N0LWluLXRoZS13aXJlL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL3dyaXRlb3Mtc2NyaWJlLXRlcm1pbmFsL3dyaXRlb3Mtc2NyaWJlLXRlcm1pbmFsL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"writeOS-scribe-terminal\",\n    description: \"Sovereign scroll editor + AI assistant app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"bg-zinc-950 text-white min-h-screen\",\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDdUI7QUFFaEIsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBVTtzQkFDYko7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9ob21lL2dob3N0LWluLXRoZS13aXJlL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL3dyaXRlb3Mtc2NyaWJlLXRlcm1pbmFsL3dyaXRlb3Mtc2NyaWJlLXRlcm1pbmFsL2FwcC9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJ3cml0ZU9TLXNjcmliZS10ZXJtaW5hbFwiLFxuICBkZXNjcmlwdGlvbjogXCJTb3ZlcmVpZ24gc2Nyb2xsIGVkaXRvciArIEFJIGFzc2lzdGFudCBhcHBcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9XCJiZy16aW5jLTk1MCB0ZXh0LXdoaXRlIG1pbi1oLXNjcmVlblwiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_components_TemplateSidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../src/components/TemplateSidebar */ \"(rsc)/./src/components/TemplateSidebar.tsx\");\n/* harmony import */ var _src_components_ScrollEditor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../src/components/ScrollEditor */ \"(rsc)/./src/components/ScrollEditor.tsx\");\n/* harmony import */ var _src_components_ScribeChatPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../src/components/ScribeChatPanel */ \"(rsc)/./src/components/ScribeChatPanel.tsx\");\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"grid grid-cols-[260px_1fr_1fr] h-screen w-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-r border-zinc-800 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_TemplateSidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-r border-zinc-800 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ScrollEditor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_ScribeChatPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/app/page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFnRTtBQUNOO0FBQ007QUFFakQsU0FBU0c7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUtDLFdBQVU7OzBCQUVkLDhEQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0wsdUVBQWVBOzs7Ozs7Ozs7OzBCQUlsQiw4REFBQ007Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNKLG9FQUFZQTs7Ozs7Ozs7OzswQkFJZiw4REFBQ0s7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNILHVFQUFlQTs7Ozs7Ozs7Ozs7Ozs7OztBQUl4QiIsInNvdXJjZXMiOlsiL2hvbWUvZ2hvc3QtaW4tdGhlLXdpcmUvRG9jdW1lbnRzL2F1Z21lbnQtcHJvamVjdHMvd3JpdGVvcy1zY3JpYmUtdGVybWluYWwvd3JpdGVvcy1zY3JpYmUtdGVybWluYWwvYXBwL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBUZW1wbGF0ZVNpZGViYXIgZnJvbSBcIi4uL3NyYy9jb21wb25lbnRzL1RlbXBsYXRlU2lkZWJhclwiO1xuaW1wb3J0IFNjcm9sbEVkaXRvciBmcm9tIFwiLi4vc3JjL2NvbXBvbmVudHMvU2Nyb2xsRWRpdG9yXCI7XG5pbXBvcnQgU2NyaWJlQ2hhdFBhbmVsIGZyb20gXCIuLi9zcmMvY29tcG9uZW50cy9TY3JpYmVDaGF0UGFuZWxcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8bWFpbiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy1bMjYwcHhfMWZyXzFmcl0gaC1zY3JlZW4gdy1mdWxsIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgey8qIFNpZGViYXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1yIGJvcmRlci16aW5jLTgwMCBwLTRcIj5cbiAgICAgICAgPFRlbXBsYXRlU2lkZWJhciAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNYXJrZG93biBFZGl0b3IgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1yIGJvcmRlci16aW5jLTgwMCBwLTRcIj5cbiAgICAgICAgPFNjcm9sbEVkaXRvciAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDaGF0IEFzc2lzdGFudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgIDxTY3JpYmVDaGF0UGFuZWwgLz5cbiAgICAgIDwvZGl2PlxuICAgIDwvbWFpbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJUZW1wbGF0ZVNpZGViYXIiLCJTY3JvbGxFZGl0b3IiLCJTY3JpYmVDaGF0UGFuZWwiLCJIb21lIiwibWFpbiIsImNsYXNzTmFtZSIsImRpdiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ScribeChatPanel.tsx":
/*!********************************************!*\
  !*** ./src/components/ScribeChatPanel.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ScrollEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/ScrollEditor.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/TemplateSidebar.tsx":
/*!********************************************!*\
  !*** ./src/components/TemplateSidebar.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2F.npm%2F_npx%2F90eb7c78286cc043%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScribeChatPanel.tsx */ \"(ssr)/./src/components/ScribeChatPanel.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScrollEditor.tsx */ \"(ssr)/./src/components/ScrollEditor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/TemplateSidebar.tsx */ \"(ssr)/./src/components/TemplateSidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/ScribeChatPanel.tsx":
/*!********************************************!*\
  !*** ./src/components/ScribeChatPanel.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScribeChatPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ScribeChatPanel() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            role: 'assistant',\n            content: 'Welcome to your AI writing assistant. How can I help you craft your scroll today?',\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScribeChatPanel.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ScribeChatPanel.useEffect\"], [\n        messages\n    ]);\n    const sendMessage = async ()=>{\n        if (!input.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: input.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput('');\n        setIsLoading(true);\n        try {\n            // Simulate AI response (replace with actual AI API call)\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                role: 'assistant',\n                content: generateAIResponse(userMessage.content),\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiResponse\n                ]);\n        } catch (error) {\n            console.error('Error sending message:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const generateAIResponse = (userInput)=>{\n        // Enhanced AI response logic\n        const responses = {\n            writing: [\n                \"I can help you structure your thoughts. What type of document are you working on?\",\n                \"Let's break this down into clear sections. What's your main objective?\",\n                \"Consider starting with an outline. What are the key points you want to cover?\"\n            ],\n            editing: [\n                \"I can help refine your prose. Share a paragraph and I'll suggest improvements.\",\n                \"Let's focus on clarity and flow. What section needs the most work?\",\n                \"Consider your audience - who are you writing for?\"\n            ],\n            ideas: [\n                \"Let's brainstorm! What's the core concept you want to explore?\",\n                \"I can help generate creative angles. What's your starting point?\",\n                \"What if we approached this from a different perspective?\"\n            ],\n            default: [\n                \"I'm here to help with your writing. What would you like to work on?\",\n                \"Let's craft something amazing together. What's on your mind?\",\n                \"I can assist with writing, editing, brainstorming, or structuring. How can I help?\"\n            ]\n        };\n        const input = userInput.toLowerCase();\n        if (input.includes('write') || input.includes('draft')) {\n            return responses.writing[Math.floor(Math.random() * responses.writing.length)];\n        } else if (input.includes('edit') || input.includes('improve') || input.includes('fix')) {\n            return responses.editing[Math.floor(Math.random() * responses.editing.length)];\n        } else if (input.includes('idea') || input.includes('brainstorm') || input.includes('think')) {\n            return responses.ideas[Math.floor(Math.random() * responses.ideas.length)];\n        } else {\n            return responses.default[Math.floor(Math.random() * responses.default.length)];\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-flame text-lg font-bold mb-4\",\n                children: \"\\uD83E\\uDD16 Scribe Assistant\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg p-4 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto mb-4 space-y-3\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `max-w-[80%] p-3 rounded-lg text-sm ${message.role === 'user' ? 'bg-flame text-white' : 'bg-zinc-700 text-zinc-100'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-60 mt-1\",\n                                                children: message.timestamp.toLocaleTimeString([], {\n                                                    hour: '2-digit',\n                                                    minute: '2-digit'\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                }, message.id, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-zinc-700 text-zinc-100 p-3 rounded-lg text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Thinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                className: \"flex-1 bg-zinc-800 border border-zinc-700 rounded px-3 py-2 text-white text-sm focus:outline-none focus:border-ghostblue\",\n                                placeholder: \"Ask your scribe assistant...\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: sendMessage,\n                                disabled: !input.trim() || isLoading,\n                                className: \"bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded text-sm font-medium transition-colors\",\n                                children: \"Send\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TY3JpYmVDaGF0UGFuZWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVvRDtBQVNyQyxTQUFTRztJQUN0QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR0wsK0NBQVFBLENBQVk7UUFDbEQ7WUFDRU0sSUFBSTtZQUNKQyxNQUFNO1lBQ05DLFNBQVM7WUFDVEMsV0FBVyxJQUFJQztRQUNqQjtLQUNEO0lBQ0QsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUdaLCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ2EsV0FBV0MsYUFBYSxHQUFHZCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNZSxpQkFBaUJkLDZDQUFNQSxDQUFpQjtJQUU5QyxNQUFNZSxpQkFBaUI7UUFDckJELGVBQWVFLE9BQU8sRUFBRUMsZUFBZTtZQUFFQyxVQUFVO1FBQVM7SUFDOUQ7SUFFQWpCLGdEQUFTQTtxQ0FBQztZQUNSYztRQUNGO29DQUFHO1FBQUNaO0tBQVM7SUFFYixNQUFNZ0IsY0FBYztRQUNsQixJQUFJLENBQUNULE1BQU1VLElBQUksTUFBTVIsV0FBVztRQUVoQyxNQUFNUyxjQUF1QjtZQUMzQmhCLElBQUlJLEtBQUthLEdBQUcsR0FBR0MsUUFBUTtZQUN2QmpCLE1BQU07WUFDTkMsU0FBU0csTUFBTVUsSUFBSTtZQUNuQlosV0FBVyxJQUFJQztRQUNqQjtRQUVBTCxZQUFZb0IsQ0FBQUEsT0FBUTttQkFBSUE7Z0JBQU1IO2FBQVk7UUFDMUNWLFNBQVM7UUFDVEUsYUFBYTtRQUViLElBQUk7WUFDRix5REFBeUQ7WUFDekQsTUFBTSxJQUFJWSxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO1lBRWpELE1BQU1FLGFBQXNCO2dCQUMxQnZCLElBQUksQ0FBQ0ksS0FBS2EsR0FBRyxLQUFLLEdBQUdDLFFBQVE7Z0JBQzdCakIsTUFBTTtnQkFDTkMsU0FBU3NCLG1CQUFtQlIsWUFBWWQsT0FBTztnQkFDL0NDLFdBQVcsSUFBSUM7WUFDakI7WUFFQUwsWUFBWW9CLENBQUFBLE9BQVE7dUJBQUlBO29CQUFNSTtpQkFBVztRQUMzQyxFQUFFLE9BQU9FLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7UUFDMUMsU0FBVTtZQUNSakIsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNZ0IscUJBQXFCLENBQUNHO1FBQzFCLDZCQUE2QjtRQUM3QixNQUFNQyxZQUFZO1lBQ2hCQyxTQUFTO2dCQUNQO2dCQUNBO2dCQUNBO2FBQ0Q7WUFDREMsU0FBUztnQkFDUDtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLE9BQU87Z0JBQ0w7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNEQyxTQUFTO2dCQUNQO2dCQUNBO2dCQUNBO2FBQ0Q7UUFDSDtRQUVBLE1BQU0zQixRQUFRc0IsVUFBVU0sV0FBVztRQUNuQyxJQUFJNUIsTUFBTTZCLFFBQVEsQ0FBQyxZQUFZN0IsTUFBTTZCLFFBQVEsQ0FBQyxVQUFVO1lBQ3RELE9BQU9OLFVBQVVDLE9BQU8sQ0FBQ00sS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUtULFVBQVVDLE9BQU8sQ0FBQ1MsTUFBTSxFQUFFO1FBQ2hGLE9BQU8sSUFBSWpDLE1BQU02QixRQUFRLENBQUMsV0FBVzdCLE1BQU02QixRQUFRLENBQUMsY0FBYzdCLE1BQU02QixRQUFRLENBQUMsUUFBUTtZQUN2RixPQUFPTixVQUFVRSxPQUFPLENBQUNLLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLVCxVQUFVRSxPQUFPLENBQUNRLE1BQU0sRUFBRTtRQUNoRixPQUFPLElBQUlqQyxNQUFNNkIsUUFBUSxDQUFDLFdBQVc3QixNQUFNNkIsUUFBUSxDQUFDLGlCQUFpQjdCLE1BQU02QixRQUFRLENBQUMsVUFBVTtZQUM1RixPQUFPTixVQUFVRyxLQUFLLENBQUNJLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLVCxVQUFVRyxLQUFLLENBQUNPLE1BQU0sRUFBRTtRQUM1RSxPQUFPO1lBQ0wsT0FBT1YsVUFBVUksT0FBTyxDQUFDRyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS1QsVUFBVUksT0FBTyxDQUFDTSxNQUFNLEVBQUU7UUFDaEY7SUFDRjtJQUVBLE1BQU1DLGlCQUFpQixDQUFDQztRQUN0QixJQUFJQSxFQUFFQyxHQUFHLEtBQUssV0FBVyxDQUFDRCxFQUFFRSxRQUFRLEVBQUU7WUFDcENGLEVBQUVHLGNBQWM7WUFDaEI3QjtRQUNGO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQzhCO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFBb0M7Ozs7OzswQkFDbkQsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzRCQUNaL0MsU0FBU2dELEdBQUcsQ0FBQyxDQUFDQyx3QkFDYiw4REFBQ0g7b0NBRUNDLFdBQVcsQ0FBQyxLQUFLLEVBQUVFLFFBQVE5QyxJQUFJLEtBQUssU0FBUyxnQkFBZ0IsaUJBQWlCOzhDQUU5RSw0RUFBQzJDO3dDQUNDQyxXQUFXLENBQUMsbUNBQW1DLEVBQzdDRSxRQUFROUMsSUFBSSxLQUFLLFNBQ2Isd0JBQ0EsNkJBQ0o7OzBEQUVGLDhEQUFDMkM7MERBQUtHLFFBQVE3QyxPQUFPOzs7Ozs7MERBQ3JCLDhEQUFDMEM7Z0RBQUlDLFdBQVU7MERBQ1pFLFFBQVE1QyxTQUFTLENBQUM2QyxrQkFBa0IsQ0FBQyxFQUFFLEVBQUU7b0RBQUVDLE1BQU07b0RBQVdDLFFBQVE7Z0RBQVU7Ozs7Ozs7Ozs7OzttQ0FaOUVILFFBQVEvQyxFQUFFOzs7Ozs0QkFpQmxCTywyQkFDQyw4REFBQ3FDO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzs7Ozs7a0VBQ2YsOERBQUNEO3dEQUFJQyxXQUFVO3dEQUFtRE0sT0FBTzs0REFBQ0MsZ0JBQWdCO3dEQUFNOzs7Ozs7a0VBQ2hHLDhEQUFDUjt3REFBSUMsV0FBVTt3REFBbURNLE9BQU87NERBQUNDLGdCQUFnQjt3REFBTTs7Ozs7Ozs7Ozs7OzBEQUVsRyw4REFBQ0M7MERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS2QsOERBQUNUO2dDQUFJVSxLQUFLN0M7Ozs7Ozs7Ozs7OztrQ0FFWiw4REFBQ21DO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3hDO2dDQUNDa0QsTUFBSztnQ0FDTEMsT0FBT25EO2dDQUNQb0QsVUFBVSxDQUFDakIsSUFBTWxDLFNBQVNrQyxFQUFFa0IsTUFBTSxDQUFDRixLQUFLO2dDQUN4Q0csWUFBWXBCO2dDQUNaTSxXQUFVO2dDQUNWZSxhQUFZO2dDQUNaQyxVQUFVdEQ7Ozs7OzswQ0FFWiw4REFBQ3VEO2dDQUNDQyxTQUFTakQ7Z0NBQ1QrQyxVQUFVLENBQUN4RCxNQUFNVSxJQUFJLE1BQU1SO2dDQUMzQnNDLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9YIiwic291cmNlcyI6WyIvaG9tZS9naG9zdC1pbi10aGUtd2lyZS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy93cml0ZW9zLXNjcmliZS10ZXJtaW5hbC93cml0ZW9zLXNjcmliZS10ZXJtaW5hbC9zcmMvY29tcG9uZW50cy9TY3JpYmVDaGF0UGFuZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbmludGVyZmFjZSBNZXNzYWdlIHtcbiAgaWQ6IHN0cmluZztcbiAgcm9sZTogJ3VzZXInIHwgJ2Fzc2lzdGFudCc7XG4gIGNvbnRlbnQ6IHN0cmluZztcbiAgdGltZXN0YW1wOiBEYXRlO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTY3JpYmVDaGF0UGFuZWwoKSB7XG4gIGNvbnN0IFttZXNzYWdlcywgc2V0TWVzc2FnZXNdID0gdXNlU3RhdGU8TWVzc2FnZVtdPihbXG4gICAge1xuICAgICAgaWQ6ICcxJyxcbiAgICAgIHJvbGU6ICdhc3Npc3RhbnQnLFxuICAgICAgY29udGVudDogJ1dlbGNvbWUgdG8geW91ciBBSSB3cml0aW5nIGFzc2lzdGFudC4gSG93IGNhbiBJIGhlbHAgeW91IGNyYWZ0IHlvdXIgc2Nyb2xsIHRvZGF5PycsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKClcbiAgICB9XG4gIF0pO1xuICBjb25zdCBbaW5wdXQsIHNldElucHV0XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgbWVzc2FnZXNFbmRSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xuXG4gIGNvbnN0IHNjcm9sbFRvQm90dG9tID0gKCkgPT4ge1xuICAgIG1lc3NhZ2VzRW5kUmVmLmN1cnJlbnQ/LnNjcm9sbEludG9WaWV3KHsgYmVoYXZpb3I6IFwic21vb3RoXCIgfSk7XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzY3JvbGxUb0JvdHRvbSgpO1xuICB9LCBbbWVzc2FnZXNdKTtcblxuICBjb25zdCBzZW5kTWVzc2FnZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIWlucHV0LnRyaW0oKSB8fCBpc0xvYWRpbmcpIHJldHVybjtcblxuICAgIGNvbnN0IHVzZXJNZXNzYWdlOiBNZXNzYWdlID0ge1xuICAgICAgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSxcbiAgICAgIHJvbGU6ICd1c2VyJyxcbiAgICAgIGNvbnRlbnQ6IGlucHV0LnRyaW0oKSxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKVxuICAgIH07XG5cbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCB1c2VyTWVzc2FnZV0pO1xuICAgIHNldElucHV0KCcnKTtcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gU2ltdWxhdGUgQUkgcmVzcG9uc2UgKHJlcGxhY2Ugd2l0aCBhY3R1YWwgQUkgQVBJIGNhbGwpXG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCkpO1xuXG4gICAgICBjb25zdCBhaVJlc3BvbnNlOiBNZXNzYWdlID0ge1xuICAgICAgICBpZDogKERhdGUubm93KCkgKyAxKS50b1N0cmluZygpLFxuICAgICAgICByb2xlOiAnYXNzaXN0YW50JyxcbiAgICAgICAgY29udGVudDogZ2VuZXJhdGVBSVJlc3BvbnNlKHVzZXJNZXNzYWdlLmNvbnRlbnQpLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKClcbiAgICAgIH07XG5cbiAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIGFpUmVzcG9uc2VdKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2VuZGluZyBtZXNzYWdlOicsIGVycm9yKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2VuZXJhdGVBSVJlc3BvbnNlID0gKHVzZXJJbnB1dDogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgICAvLyBFbmhhbmNlZCBBSSByZXNwb25zZSBsb2dpY1xuICAgIGNvbnN0IHJlc3BvbnNlcyA9IHtcbiAgICAgIHdyaXRpbmc6IFtcbiAgICAgICAgXCJJIGNhbiBoZWxwIHlvdSBzdHJ1Y3R1cmUgeW91ciB0aG91Z2h0cy4gV2hhdCB0eXBlIG9mIGRvY3VtZW50IGFyZSB5b3Ugd29ya2luZyBvbj9cIixcbiAgICAgICAgXCJMZXQncyBicmVhayB0aGlzIGRvd24gaW50byBjbGVhciBzZWN0aW9ucy4gV2hhdCdzIHlvdXIgbWFpbiBvYmplY3RpdmU/XCIsXG4gICAgICAgIFwiQ29uc2lkZXIgc3RhcnRpbmcgd2l0aCBhbiBvdXRsaW5lLiBXaGF0IGFyZSB0aGUga2V5IHBvaW50cyB5b3Ugd2FudCB0byBjb3Zlcj9cIlxuICAgICAgXSxcbiAgICAgIGVkaXRpbmc6IFtcbiAgICAgICAgXCJJIGNhbiBoZWxwIHJlZmluZSB5b3VyIHByb3NlLiBTaGFyZSBhIHBhcmFncmFwaCBhbmQgSSdsbCBzdWdnZXN0IGltcHJvdmVtZW50cy5cIixcbiAgICAgICAgXCJMZXQncyBmb2N1cyBvbiBjbGFyaXR5IGFuZCBmbG93LiBXaGF0IHNlY3Rpb24gbmVlZHMgdGhlIG1vc3Qgd29yaz9cIixcbiAgICAgICAgXCJDb25zaWRlciB5b3VyIGF1ZGllbmNlIC0gd2hvIGFyZSB5b3Ugd3JpdGluZyBmb3I/XCJcbiAgICAgIF0sXG4gICAgICBpZGVhczogW1xuICAgICAgICBcIkxldCdzIGJyYWluc3Rvcm0hIFdoYXQncyB0aGUgY29yZSBjb25jZXB0IHlvdSB3YW50IHRvIGV4cGxvcmU/XCIsXG4gICAgICAgIFwiSSBjYW4gaGVscCBnZW5lcmF0ZSBjcmVhdGl2ZSBhbmdsZXMuIFdoYXQncyB5b3VyIHN0YXJ0aW5nIHBvaW50P1wiLFxuICAgICAgICBcIldoYXQgaWYgd2UgYXBwcm9hY2hlZCB0aGlzIGZyb20gYSBkaWZmZXJlbnQgcGVyc3BlY3RpdmU/XCJcbiAgICAgIF0sXG4gICAgICBkZWZhdWx0OiBbXG4gICAgICAgIFwiSSdtIGhlcmUgdG8gaGVscCB3aXRoIHlvdXIgd3JpdGluZy4gV2hhdCB3b3VsZCB5b3UgbGlrZSB0byB3b3JrIG9uP1wiLFxuICAgICAgICBcIkxldCdzIGNyYWZ0IHNvbWV0aGluZyBhbWF6aW5nIHRvZ2V0aGVyLiBXaGF0J3Mgb24geW91ciBtaW5kP1wiLFxuICAgICAgICBcIkkgY2FuIGFzc2lzdCB3aXRoIHdyaXRpbmcsIGVkaXRpbmcsIGJyYWluc3Rvcm1pbmcsIG9yIHN0cnVjdHVyaW5nLiBIb3cgY2FuIEkgaGVscD9cIlxuICAgICAgXVxuICAgIH07XG5cbiAgICBjb25zdCBpbnB1dCA9IHVzZXJJbnB1dC50b0xvd2VyQ2FzZSgpO1xuICAgIGlmIChpbnB1dC5pbmNsdWRlcygnd3JpdGUnKSB8fCBpbnB1dC5pbmNsdWRlcygnZHJhZnQnKSkge1xuICAgICAgcmV0dXJuIHJlc3BvbnNlcy53cml0aW5nW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHJlc3BvbnNlcy53cml0aW5nLmxlbmd0aCldO1xuICAgIH0gZWxzZSBpZiAoaW5wdXQuaW5jbHVkZXMoJ2VkaXQnKSB8fCBpbnB1dC5pbmNsdWRlcygnaW1wcm92ZScpIHx8IGlucHV0LmluY2x1ZGVzKCdmaXgnKSkge1xuICAgICAgcmV0dXJuIHJlc3BvbnNlcy5lZGl0aW5nW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHJlc3BvbnNlcy5lZGl0aW5nLmxlbmd0aCldO1xuICAgIH0gZWxzZSBpZiAoaW5wdXQuaW5jbHVkZXMoJ2lkZWEnKSB8fCBpbnB1dC5pbmNsdWRlcygnYnJhaW5zdG9ybScpIHx8IGlucHV0LmluY2x1ZGVzKCd0aGluaycpKSB7XG4gICAgICByZXR1cm4gcmVzcG9uc2VzLmlkZWFzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHJlc3BvbnNlcy5pZGVhcy5sZW5ndGgpXTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIHJlc3BvbnNlcy5kZWZhdWx0W01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHJlc3BvbnNlcy5kZWZhdWx0Lmxlbmd0aCldO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVLZXlQcmVzcyA9IChlOiBSZWFjdC5LZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgaWYgKGUua2V5ID09PSAnRW50ZXInICYmICFlLnNoaWZ0S2V5KSB7XG4gICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICBzZW5kTWVzc2FnZSgpO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGZsZXggZmxleC1jb2xcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1mbGFtZSB0ZXh0LWxnIGZvbnQtYm9sZCBtYi00XCI+8J+kliBTY3JpYmUgQXNzaXN0YW50PC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBiZy1zY3JvbGxiZyBib3JkZXIgYm9yZGVyLXNoYWRvd2xpbmUgcm91bmRlZC1sZyBwLTQgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gbWItNCBzcGFjZS15LTNcIj5cbiAgICAgICAgICB7bWVzc2FnZXMubWFwKChtZXNzYWdlKSA9PiAoXG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIGtleT17bWVzc2FnZS5pZH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCAke21lc3NhZ2Uucm9sZSA9PT0gJ3VzZXInID8gJ2p1c3RpZnktZW5kJyA6ICdqdXN0aWZ5LXN0YXJ0J31gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgbWF4LXctWzgwJV0gcC0zIHJvdW5kZWQtbGcgdGV4dC1zbSAke1xuICAgICAgICAgICAgICAgICAgbWVzc2FnZS5yb2xlID09PSAndXNlcidcbiAgICAgICAgICAgICAgICAgICAgPyAnYmctZmxhbWUgdGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICAgICAgOiAnYmctemluYy03MDAgdGV4dC16aW5jLTEwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXY+e21lc3NhZ2UuY29udGVudH08L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgb3BhY2l0eS02MCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICB7bWVzc2FnZS50aW1lc3RhbXAudG9Mb2NhbGVUaW1lU3RyaW5nKFtdLCB7IGhvdXI6ICcyLWRpZ2l0JywgbWludXRlOiAnMi1kaWdpdCcgfSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgICAge2lzTG9hZGluZyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1zdGFydFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXppbmMtNzAwIHRleHQtemluYy0xMDAgcC0zIHJvdW5kZWQtbGcgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWdob3N0Ymx1ZSByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2VcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWdob3N0Ymx1ZSByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2VcIiBzdHlsZT17e2FuaW1hdGlvbkRlbGF5OiAnMC4xcyd9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWdob3N0Ymx1ZSByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2VcIiBzdHlsZT17e2FuaW1hdGlvbkRlbGF5OiAnMC4ycyd9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNwYW4+VGhpbmtpbmcuLi48L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgICA8ZGl2IHJlZj17bWVzc2FnZXNFbmRSZWZ9IC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgIHZhbHVlPXtpbnB1dH1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0SW5wdXQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgb25LZXlQcmVzcz17aGFuZGxlS2V5UHJlc3N9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYmctemluYy04MDAgYm9yZGVyIGJvcmRlci16aW5jLTcwMCByb3VuZGVkIHB4LTMgcHktMiB0ZXh0LXdoaXRlIHRleHQtc20gZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOmJvcmRlci1naG9zdGJsdWVcIlxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJBc2sgeW91ciBzY3JpYmUgYXNzaXN0YW50Li4uXCJcbiAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmd9XG4gICAgICAgICAgLz5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtzZW5kTWVzc2FnZX1cbiAgICAgICAgICAgIGRpc2FibGVkPXshaW5wdXQudHJpbSgpIHx8IGlzTG9hZGluZ31cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWZsYW1lIGhvdmVyOmJnLWZsYW1lLzgwIGRpc2FibGVkOmJnLXppbmMtNjAwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCB0ZXh0LXdoaXRlIHB4LTQgcHktMiByb3VuZGVkIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIFNlbmRcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsIlNjcmliZUNoYXRQYW5lbCIsIm1lc3NhZ2VzIiwic2V0TWVzc2FnZXMiLCJpZCIsInJvbGUiLCJjb250ZW50IiwidGltZXN0YW1wIiwiRGF0ZSIsImlucHV0Iiwic2V0SW5wdXQiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJtZXNzYWdlc0VuZFJlZiIsInNjcm9sbFRvQm90dG9tIiwiY3VycmVudCIsInNjcm9sbEludG9WaWV3IiwiYmVoYXZpb3IiLCJzZW5kTWVzc2FnZSIsInRyaW0iLCJ1c2VyTWVzc2FnZSIsIm5vdyIsInRvU3RyaW5nIiwicHJldiIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImFpUmVzcG9uc2UiLCJnZW5lcmF0ZUFJUmVzcG9uc2UiLCJlcnJvciIsImNvbnNvbGUiLCJ1c2VySW5wdXQiLCJyZXNwb25zZXMiLCJ3cml0aW5nIiwiZWRpdGluZyIsImlkZWFzIiwiZGVmYXVsdCIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJNYXRoIiwiZmxvb3IiLCJyYW5kb20iLCJsZW5ndGgiLCJoYW5kbGVLZXlQcmVzcyIsImUiLCJrZXkiLCJzaGlmdEtleSIsInByZXZlbnREZWZhdWx0IiwiZGl2IiwiY2xhc3NOYW1lIiwibWFwIiwibWVzc2FnZSIsInRvTG9jYWxlVGltZVN0cmluZyIsImhvdXIiLCJtaW51dGUiLCJzdHlsZSIsImFuaW1hdGlvbkRlbGF5Iiwic3BhbiIsInJlZiIsInR5cGUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0Iiwib25LZXlQcmVzcyIsInBsYWNlaG9sZGVyIiwiZGlzYWJsZWQiLCJidXR0b24iLCJvbkNsaWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ScribeChatPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ScrollEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/ScrollEditor.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ScrollEditor() {\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isPreview, setIsPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            if (content.trim()) {\n                const timer = setTimeout({\n                    \"ScrollEditor.useEffect.timer\": ()=>{\n                        // Simulate auto-save\n                        localStorage.setItem('scroll-content', content);\n                        setLastSaved(new Date());\n                    }\n                }[\"ScrollEditor.useEffect.timer\"], 2000);\n                return ({\n                    \"ScrollEditor.useEffect\": ()=>clearTimeout(timer)\n                })[\"ScrollEditor.useEffect\"];\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], [\n        content\n    ]);\n    // Load saved content on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const saved = localStorage.getItem('scroll-content');\n            if (saved) {\n                setContent(saved);\n                setLastSaved(new Date());\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Listen for template loading events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const handleLoadTemplate = {\n                \"ScrollEditor.useEffect.handleLoadTemplate\": (event)=>{\n                    const template = event.detail;\n                    setContent(template.content);\n                    setLastSaved(null); // Reset save status for new template\n                    // Focus the editor\n                    setTimeout({\n                        \"ScrollEditor.useEffect.handleLoadTemplate\": ()=>{\n                            textareaRef.current?.focus();\n                        }\n                    }[\"ScrollEditor.useEffect.handleLoadTemplate\"], 100);\n                }\n            }[\"ScrollEditor.useEffect.handleLoadTemplate\"];\n            window.addEventListener('loadTemplate', handleLoadTemplate);\n            return ({\n                \"ScrollEditor.useEffect\": ()=>{\n                    window.removeEventListener('loadTemplate', handleLoadTemplate);\n                }\n            })[\"ScrollEditor.useEffect\"];\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Calculate stats\n    const wordCount = content.trim() ? content.trim().split(/\\s+/).length : 0;\n    const charCount = content.length;\n    const lineCount = content.split('\\n').length;\n    // Simple markdown preview (basic implementation)\n    const renderMarkdown = (text)=>{\n        return text.replace(/^# (.*$)/gim, '<h1 class=\"text-2xl font-bold text-ghostblue mb-4\">$1</h1>').replace(/^## (.*$)/gim, '<h2 class=\"text-xl font-bold text-white mb-3\">$1</h2>').replace(/^### (.*$)/gim, '<h3 class=\"text-lg font-bold text-zinc-300 mb-2\">$1</h3>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong class=\"font-bold text-white\">$1</strong>').replace(/\\*(.*?)\\*/g, '<em class=\"italic text-zinc-300\">$1</em>').replace(/`(.*?)`/g, '<code class=\"bg-zinc-800 text-flame px-1 rounded\">$1</code>').replace(/\\n/g, '<br>');\n    };\n    const handleKeyDown = (e)=>{\n        // Tab support\n        if (e.key === 'Tab') {\n            e.preventDefault();\n            const start = e.currentTarget.selectionStart;\n            const end = e.currentTarget.selectionEnd;\n            const newContent = content.substring(0, start) + '  ' + content.substring(end);\n            setContent(newContent);\n            // Restore cursor position\n            setTimeout(()=>{\n                if (textareaRef.current) {\n                    textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 2;\n                }\n            }, 0);\n        }\n    };\n    const insertTemplate = (template)=>{\n        const templates = {\n            heading: '# Your Heading Here\\n\\n',\n            list: '- Item 1\\n- Item 2\\n- Item 3\\n\\n',\n            code: '```\\nYour code here\\n```\\n\\n',\n            quote: '> Your quote here\\n\\n',\n            table: '| Column 1 | Column 2 |\\n|----------|----------|\\n| Cell 1   | Cell 2   |\\n\\n'\n        };\n        const insertion = templates[template] || '';\n        const textarea = textareaRef.current;\n        if (textarea) {\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const newContent = content.substring(0, start) + insertion + content.substring(end);\n            setContent(newContent);\n            // Focus and position cursor\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.selectionStart = textarea.selectionEnd = start + insertion.length;\n            }, 0);\n        }\n    };\n    const handleAscend = async (action)=>{\n        if (!content.trim() || isProcessing) return;\n        setIsProcessing(true);\n        try {\n            const response = await fetch('/api/ascend', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content,\n                    action,\n                    context: 'scroll-editor'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setContent(result.processedContent);\n                setLastSaved(null); // Reset save status\n                // Show success notification (you could add a toast here)\n                console.log('Ascend completed:', result.message);\n                console.log('Suggestions:', result.suggestions);\n            } else {\n                console.error('Ascend failed:', result.error);\n            }\n        } catch (error) {\n            console.error('Ascend error:', error);\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-ghostblue text-lg font-bold\",\n                        children: \"✍️ Scroll Editor\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsPreview(!isPreview),\n                                className: `px-3 py-1 rounded text-xs font-medium transition-colors ${isPreview ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-700 text-white hover:bg-zinc-600'}`,\n                                children: isPreview ? '📝 Edit' : '👁️ Preview'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    '🔥 Enhance',\n                                    '📋 Summarize',\n                                    '🎨 Format',\n                                    '📊 Analyze'\n                                ].map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleAscend(action.split(' ')[1].toLowerCase()),\n                                        className: \"px-2 py-1 bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white text-xs rounded font-medium transition-colors\",\n                                        disabled: !content.trim() || isProcessing,\n                                        children: isProcessing ? '⏳' : action\n                                    }, action, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-zinc-500\",\n                                children: lastSaved ? `Saved ${lastSaved.toLocaleTimeString()}` : 'Not saved'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-1 mb-3\",\n                children: [\n                    {\n                        label: 'H1',\n                        action: ()=>insertTemplate('heading')\n                    },\n                    {\n                        label: 'List',\n                        action: ()=>insertTemplate('list')\n                    },\n                    {\n                        label: 'Code',\n                        action: ()=>insertTemplate('code')\n                    },\n                    {\n                        label: 'Quote',\n                        action: ()=>insertTemplate('quote')\n                    },\n                    {\n                        label: 'Table',\n                        action: ()=>insertTemplate('table')\n                    }\n                ].map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: tool.action,\n                        className: \"px-2 py-1 bg-zinc-800 hover:bg-zinc-700 text-zinc-300 text-xs rounded transition-colors\",\n                        children: tool.label\n                    }, tool.label, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg overflow-hidden\",\n                children: isPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-4 overflow-y-auto prose prose-invert max-w-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white leading-relaxed\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(content)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: content,\n                    onChange: (e)=>setContent(e.target.value),\n                    onKeyDown: handleKeyDown,\n                    className: \"w-full h-full bg-transparent text-white resize-none outline-none p-4 font-mono text-sm leading-relaxed\",\n                    placeholder: \"Begin your scroll here...  # Welcome to your Scroll Editor  Start writing in **Markdown** format: - Use # for headings - Use **bold** and *italic* text - Create `code snippets` - Make lists and more!  Press Tab for indentation, Ctrl+Enter for preview.\",\n                    spellCheck: false\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 flex items-center justify-between text-xs text-zinc-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Words: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: wordCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Characters: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: charCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Lines: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: lineCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            content.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-ghostblue rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Ready\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ScrollEditor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TemplateSidebar.tsx":
/*!********************************************!*\
  !*** ./src/components/TemplateSidebar.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TemplateSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction TemplateSidebar() {\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const templates = [\n        {\n            id: '1',\n            name: \"Blank Scroll\",\n            icon: \"📜\",\n            content: \"# New Document\\n\\nStart writing here...\",\n            description: \"Empty document to start fresh\",\n            category: 'writing'\n        },\n        {\n            id: '2',\n            name: \"Technical Doc\",\n            icon: \"⚙️\",\n            content: \"# Technical Documentation\\n\\n## Overview\\n\\n## Requirements\\n\\n## Implementation\\n\\n## Testing\\n\\n## Deployment\",\n            description: \"Structure for technical documentation\",\n            category: 'technical'\n        },\n        {\n            id: '3',\n            name: \"Creative Writing\",\n            icon: \"✨\",\n            content: \"# Story Title\\n\\n*Genre: [Your Genre]*\\n\\n## Characters\\n\\n## Plot Outline\\n\\n## Chapter 1\\n\\nOnce upon a time...\",\n            description: \"Template for creative stories\",\n            category: 'creative'\n        },\n        {\n            id: '4',\n            name: \"Meeting Notes\",\n            icon: \"📝\",\n            content: \"# Meeting Notes\\n\\n**Date:** [Date]\\n**Attendees:** [Names]\\n**Agenda:**\\n\\n## Discussion Points\\n\\n## Action Items\\n\\n## Next Steps\",\n            description: \"Structured meeting documentation\",\n            category: 'business'\n        },\n        {\n            id: '5',\n            name: \"Project Plan\",\n            icon: \"🎯\",\n            content: \"# Project Plan\\n\\n## Objective\\n\\n## Scope\\n\\n## Timeline\\n\\n## Resources\\n\\n## Milestones\\n\\n## Risk Assessment\",\n            description: \"Comprehensive project planning\",\n            category: 'business'\n        },\n        {\n            id: '6',\n            name: \"Blog Post\",\n            icon: \"📰\",\n            content: \"# Blog Post Title\\n\\n*Published: [Date]*\\n*Tags: [tag1, tag2]*\\n\\n## Introduction\\n\\n## Main Content\\n\\n## Conclusion\\n\\n---\\n*What do you think? Leave a comment below!*\",\n            description: \"Blog post structure\",\n            category: 'writing'\n        },\n        {\n            id: '7',\n            name: \"API Documentation\",\n            icon: \"🔌\",\n            content: \"# API Documentation\\n\\n## Endpoints\\n\\n### GET /api/endpoint\\n\\n**Description:** \\n\\n**Parameters:**\\n\\n**Response:**\\n\\n```json\\n{\\n  \\\"status\\\": \\\"success\\\"\\n}\\n```\",\n            description: \"API reference template\",\n            category: 'technical'\n        }\n    ];\n    const categories = [\n        {\n            id: 'all',\n            name: 'All',\n            icon: '📚'\n        },\n        {\n            id: 'writing',\n            name: 'Writing',\n            icon: '✍️'\n        },\n        {\n            id: 'business',\n            name: 'Business',\n            icon: '💼'\n        },\n        {\n            id: 'technical',\n            name: 'Technical',\n            icon: '⚙️'\n        },\n        {\n            id: 'creative',\n            name: 'Creative',\n            icon: '🎨'\n        }\n    ];\n    const filteredTemplates = selectedCategory === 'all' ? templates : templates.filter((t)=>t.category === selectedCategory);\n    const loadTemplate = (template)=>{\n        // Store the template content to be loaded by the editor\n        localStorage.setItem('scroll-content', template.content);\n        // Trigger a custom event to notify the editor\n        window.dispatchEvent(new CustomEvent('loadTemplate', {\n            detail: template\n        }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-flame text-lg font-bold mb-4\",\n                children: \"\\uD83D\\uDCDC Templates\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-zinc-400 mb-2\",\n                        children: \"Categories\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-1\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedCategory(category.id),\n                                className: `px-2 py-1 rounded text-xs transition-colors ${selectedCategory === category.id ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'}`,\n                                children: [\n                                    category.icon,\n                                    \" \",\n                                    category.name\n                                ]\n                            }, category.id, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 space-y-2 overflow-y-auto\",\n                children: filteredTemplates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadTemplate(template),\n                        className: \"w-full text-left p-3 rounded-lg bg-zinc-800 hover:bg-zinc-700 border border-zinc-700 hover:border-zinc-600 transition-colors group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg flex-shrink-0\",\n                                    children: template.icon\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white text-sm font-medium group-hover:text-ghostblue transition-colors\",\n                                            children: template.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-zinc-400 text-xs mt-1 line-clamp-2\",\n                                            children: template.description\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this)\n                    }, template.id, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-4 border-t border-zinc-700 space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full bg-ghostblue hover:bg-ghostblue/80 text-zinc-900 py-2 px-4 rounded font-medium text-sm transition-colors\",\n                        children: \"+ New Template\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full bg-zinc-800 hover:bg-zinc-700 text-white py-2 px-4 rounded font-medium text-sm transition-colors\",\n                        children: \"\\uD83D\\uDCC1 Browse Scrolls\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TemplateSidebar.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();