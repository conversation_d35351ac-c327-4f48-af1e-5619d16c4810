/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScribeChatPanel.tsx */ \"(app-pages-browser)/./src/components/ScribeChatPanel.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScrollEditor.tsx */ \"(app-pages-browser)/./src/components/ScrollEditor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/TemplateSidebar.tsx */ \"(app-pages-browser)/./src/components/TemplateSidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!*****************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!*******************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \*******************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi8uLi8uLi8ubnBtL19ucHgvOTBlYjdjNzgyODZjYzA0My9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixJQUFJLEtBQXFDLEVBQUUsRUFFMUMsQ0FBQztBQUNGLEVBQUUsbU9BQXNFO0FBQ3hFIiwic291cmNlcyI6WyIvaG9tZS9naG9zdC1pbi10aGUtd2lyZS8ubnBtL19ucHgvOTBlYjdjNzgyODZjYzA0My9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ScribeChatPanel.tsx":
/*!********************************************!*\
  !*** ./src/components/ScribeChatPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScribeChatPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ScribeChatPanel() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            role: 'assistant',\n            content: 'Welcome to your AI writing assistant. How can I help you craft your scroll today?',\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScribeChatPanel.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ScribeChatPanel.useEffect\"], [\n        messages\n    ]);\n    const sendMessage = async ()=>{\n        if (!input.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: input.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput('');\n        setIsLoading(true);\n        try {\n            // Simulate AI response (replace with actual AI API call)\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                role: 'assistant',\n                content: generateAIResponse(userMessage.content),\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiResponse\n                ]);\n        } catch (error) {\n            console.error('Error sending message:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const generateAIResponse = (userInput)=>{\n        // Enhanced AI response logic\n        const responses = {\n            writing: [\n                \"I can help you structure your thoughts. What type of document are you working on?\",\n                \"Let's break this down into clear sections. What's your main objective?\",\n                \"Consider starting with an outline. What are the key points you want to cover?\"\n            ],\n            editing: [\n                \"I can help refine your prose. Share a paragraph and I'll suggest improvements.\",\n                \"Let's focus on clarity and flow. What section needs the most work?\",\n                \"Consider your audience - who are you writing for?\"\n            ],\n            ideas: [\n                \"Let's brainstorm! What's the core concept you want to explore?\",\n                \"I can help generate creative angles. What's your starting point?\",\n                \"What if we approached this from a different perspective?\"\n            ],\n            default: [\n                \"I'm here to help with your writing. What would you like to work on?\",\n                \"Let's craft something amazing together. What's on your mind?\",\n                \"I can assist with writing, editing, brainstorming, or structuring. How can I help?\"\n            ]\n        };\n        const input = userInput.toLowerCase();\n        if (input.includes('write') || input.includes('draft')) {\n            return responses.writing[Math.floor(Math.random() * responses.writing.length)];\n        } else if (input.includes('edit') || input.includes('improve') || input.includes('fix')) {\n            return responses.editing[Math.floor(Math.random() * responses.editing.length)];\n        } else if (input.includes('idea') || input.includes('brainstorm') || input.includes('think')) {\n            return responses.ideas[Math.floor(Math.random() * responses.ideas.length)];\n        } else {\n            return responses.default[Math.floor(Math.random() * responses.default.length)];\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-flame text-lg font-bold mb-4\",\n                children: \"\\uD83E\\uDD16 Scribe Assistant\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg p-4 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto mb-4 space-y-3\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-[80%] p-3 rounded-lg text-sm \".concat(message.role === 'user' ? 'bg-flame text-white' : 'bg-zinc-700 text-zinc-100'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-60 mt-1\",\n                                                children: message.timestamp.toLocaleTimeString([], {\n                                                    hour: '2-digit',\n                                                    minute: '2-digit'\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                }, message.id, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-zinc-700 text-zinc-100 p-3 rounded-lg text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Thinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                className: \"flex-1 bg-zinc-800 border border-zinc-700 rounded px-3 py-2 text-white text-sm focus:outline-none focus:border-ghostblue\",\n                                placeholder: \"Ask your scribe assistant...\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: sendMessage,\n                                disabled: !input.trim() || isLoading,\n                                className: \"bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded text-sm font-medium transition-colors\",\n                                children: \"Send\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_s(ScribeChatPanel, \"a/3GhHbUQ1u1bp48TjjmheS6EE4=\");\n_c = ScribeChatPanel;\nvar _c;\n$RefreshReg$(_c, \"ScribeChatPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ScribeChatPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ScrollEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/ScrollEditor.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ScrollEditor() {\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isPreview, setIsPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            if (content.trim()) {\n                const timer = setTimeout({\n                    \"ScrollEditor.useEffect.timer\": ()=>{\n                        // Simulate auto-save\n                        localStorage.setItem('scroll-content', content);\n                        setLastSaved(new Date());\n                    }\n                }[\"ScrollEditor.useEffect.timer\"], 2000);\n                return ({\n                    \"ScrollEditor.useEffect\": ()=>clearTimeout(timer)\n                })[\"ScrollEditor.useEffect\"];\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], [\n        content\n    ]);\n    // Load saved content on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const saved = localStorage.getItem('scroll-content');\n            if (saved) {\n                setContent(saved);\n                setLastSaved(new Date());\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Listen for template loading events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const handleLoadTemplate = {\n                \"ScrollEditor.useEffect.handleLoadTemplate\": (event)=>{\n                    const template = event.detail;\n                    setContent(template.content);\n                    setLastSaved(null); // Reset save status for new template\n                    // Focus the editor\n                    setTimeout({\n                        \"ScrollEditor.useEffect.handleLoadTemplate\": ()=>{\n                            var _textareaRef_current;\n                            (_textareaRef_current = textareaRef.current) === null || _textareaRef_current === void 0 ? void 0 : _textareaRef_current.focus();\n                        }\n                    }[\"ScrollEditor.useEffect.handleLoadTemplate\"], 100);\n                }\n            }[\"ScrollEditor.useEffect.handleLoadTemplate\"];\n            window.addEventListener('loadTemplate', handleLoadTemplate);\n            return ({\n                \"ScrollEditor.useEffect\": ()=>{\n                    window.removeEventListener('loadTemplate', handleLoadTemplate);\n                }\n            })[\"ScrollEditor.useEffect\"];\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Calculate stats\n    const wordCount = content.trim() ? content.trim().split(/\\s+/).length : 0;\n    const charCount = content.length;\n    const lineCount = content.split('\\n').length;\n    // Simple markdown preview (basic implementation)\n    const renderMarkdown = (text)=>{\n        return text.replace(/^# (.*$)/gim, '<h1 class=\"text-2xl font-bold text-ghostblue mb-4\">$1</h1>').replace(/^## (.*$)/gim, '<h2 class=\"text-xl font-bold text-white mb-3\">$1</h2>').replace(/^### (.*$)/gim, '<h3 class=\"text-lg font-bold text-zinc-300 mb-2\">$1</h3>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong class=\"font-bold text-white\">$1</strong>').replace(/\\*(.*?)\\*/g, '<em class=\"italic text-zinc-300\">$1</em>').replace(/`(.*?)`/g, '<code class=\"bg-zinc-800 text-flame px-1 rounded\">$1</code>').replace(/\\n/g, '<br>');\n    };\n    const handleKeyDown = (e)=>{\n        // Tab support\n        if (e.key === 'Tab') {\n            e.preventDefault();\n            const start = e.currentTarget.selectionStart;\n            const end = e.currentTarget.selectionEnd;\n            const newContent = content.substring(0, start) + '  ' + content.substring(end);\n            setContent(newContent);\n            // Restore cursor position\n            setTimeout(()=>{\n                if (textareaRef.current) {\n                    textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 2;\n                }\n            }, 0);\n        }\n    };\n    const insertTemplate = (template)=>{\n        const templates = {\n            heading: '# Your Heading Here\\n\\n',\n            list: '- Item 1\\n- Item 2\\n- Item 3\\n\\n',\n            code: '```\\nYour code here\\n```\\n\\n',\n            quote: '> Your quote here\\n\\n',\n            table: '| Column 1 | Column 2 |\\n|----------|----------|\\n| Cell 1   | Cell 2   |\\n\\n'\n        };\n        const insertion = templates[template] || '';\n        const textarea = textareaRef.current;\n        if (textarea) {\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const newContent = content.substring(0, start) + insertion + content.substring(end);\n            setContent(newContent);\n            // Focus and position cursor\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.selectionStart = textarea.selectionEnd = start + insertion.length;\n            }, 0);\n        }\n    };\n    const handleAscend = async (action)=>{\n        if (!content.trim() || isProcessing) return;\n        setIsProcessing(true);\n        try {\n            const response = await fetch('/api/ascend', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content,\n                    action,\n                    context: 'scroll-editor'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setContent(result.processedContent);\n                setLastSaved(null); // Reset save status\n                // Show success notification (you could add a toast here)\n                console.log('Ascend completed:', result.message);\n                console.log('Suggestions:', result.suggestions);\n            } else {\n                console.error('Ascend failed:', result.error);\n            }\n        } catch (error) {\n            console.error('Ascend error:', error);\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-ghostblue text-lg font-bold\",\n                        children: \"✍️ Scroll Editor\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsPreview(!isPreview),\n                                className: \"px-3 py-1 rounded text-xs font-medium transition-colors \".concat(isPreview ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-700 text-white hover:bg-zinc-600'),\n                                children: isPreview ? '📝 Edit' : '👁️ Preview'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    '🔥 Enhance',\n                                    '📋 Summarize',\n                                    '🎨 Format',\n                                    '📊 Analyze'\n                                ].map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleAscend(action.split(' ')[1].toLowerCase()),\n                                        className: \"px-2 py-1 bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white text-xs rounded font-medium transition-colors\",\n                                        disabled: !content.trim() || isProcessing,\n                                        children: isProcessing ? '⏳' : action\n                                    }, action, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-zinc-500\",\n                                children: lastSaved ? \"Saved \".concat(lastSaved.toLocaleTimeString()) : 'Not saved'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-1 mb-3\",\n                children: [\n                    {\n                        label: 'H1',\n                        action: ()=>insertTemplate('heading')\n                    },\n                    {\n                        label: 'List',\n                        action: ()=>insertTemplate('list')\n                    },\n                    {\n                        label: 'Code',\n                        action: ()=>insertTemplate('code')\n                    },\n                    {\n                        label: 'Quote',\n                        action: ()=>insertTemplate('quote')\n                    },\n                    {\n                        label: 'Table',\n                        action: ()=>insertTemplate('table')\n                    }\n                ].map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: tool.action,\n                        className: \"px-2 py-1 bg-zinc-800 hover:bg-zinc-700 text-zinc-300 text-xs rounded transition-colors\",\n                        children: tool.label\n                    }, tool.label, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg overflow-hidden\",\n                children: isPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-4 overflow-y-auto prose prose-invert max-w-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white leading-relaxed\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(content)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: content,\n                    onChange: (e)=>setContent(e.target.value),\n                    onKeyDown: handleKeyDown,\n                    className: \"w-full h-full bg-transparent text-white resize-none outline-none p-4 font-mono text-sm leading-relaxed\",\n                    placeholder: \"Begin your scroll here...  # Welcome to your Scroll Editor  Start writing in **Markdown** format: - Use # for headings - Use **bold** and *italic* text - Create `code snippets` - Make lists and more!  Press Tab for indentation, Ctrl+Enter for preview.\",\n                    spellCheck: false\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 flex items-center justify-between text-xs text-zinc-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Words: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: wordCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Characters: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: charCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Lines: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: lineCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            content.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-ghostblue rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Ready\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, this);\n}\n_s(ScrollEditor, \"RY4gjijf6zMgAVQrPlad4F9++uo=\");\n_c = ScrollEditor;\nvar _c;\n$RefreshReg$(_c, \"ScrollEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ScrollEditor.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/TemplateSidebar.tsx":
/*!********************************************!*\
  !*** ./src/components/TemplateSidebar.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TemplateSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction TemplateSidebar() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const templates = [\n        {\n            id: '1',\n            name: \"Blank Scroll\",\n            icon: \"📜\",\n            content: \"# New Document\\n\\nStart writing here...\",\n            description: \"Empty document to start fresh\",\n            category: 'writing'\n        },\n        {\n            id: '2',\n            name: \"Technical Doc\",\n            icon: \"⚙️\",\n            content: \"# Technical Documentation\\n\\n## Overview\\n\\n## Requirements\\n\\n## Implementation\\n\\n## Testing\\n\\n## Deployment\",\n            description: \"Structure for technical documentation\",\n            category: 'technical'\n        },\n        {\n            id: '3',\n            name: \"Creative Writing\",\n            icon: \"✨\",\n            content: \"# Story Title\\n\\n*Genre: [Your Genre]*\\n\\n## Characters\\n\\n## Plot Outline\\n\\n## Chapter 1\\n\\nOnce upon a time...\",\n            description: \"Template for creative stories\",\n            category: 'creative'\n        },\n        {\n            id: '4',\n            name: \"Meeting Notes\",\n            icon: \"📝\",\n            content: \"# Meeting Notes\\n\\n**Date:** [Date]\\n**Attendees:** [Names]\\n**Agenda:**\\n\\n## Discussion Points\\n\\n## Action Items\\n\\n## Next Steps\",\n            description: \"Structured meeting documentation\",\n            category: 'business'\n        },\n        {\n            id: '5',\n            name: \"Project Plan\",\n            icon: \"🎯\",\n            content: \"# Project Plan\\n\\n## Objective\\n\\n## Scope\\n\\n## Timeline\\n\\n## Resources\\n\\n## Milestones\\n\\n## Risk Assessment\",\n            description: \"Comprehensive project planning\",\n            category: 'business'\n        },\n        {\n            id: '6',\n            name: \"Blog Post\",\n            icon: \"📰\",\n            content: \"# Blog Post Title\\n\\n*Published: [Date]*\\n*Tags: [tag1, tag2]*\\n\\n## Introduction\\n\\n## Main Content\\n\\n## Conclusion\\n\\n---\\n*What do you think? Leave a comment below!*\",\n            description: \"Blog post structure\",\n            category: 'writing'\n        },\n        {\n            id: '7',\n            name: \"API Documentation\",\n            icon: \"🔌\",\n            content: \"# API Documentation\\n\\n## Endpoints\\n\\n### GET /api/endpoint\\n\\n**Description:** \\n\\n**Parameters:**\\n\\n**Response:**\\n\\n```json\\n{\\n  \\\"status\\\": \\\"success\\\"\\n}\\n```\",\n            description: \"API reference template\",\n            category: 'technical'\n        }\n    ];\n    const categories = [\n        {\n            id: 'all',\n            name: 'All',\n            icon: '📚'\n        },\n        {\n            id: 'writing',\n            name: 'Writing',\n            icon: '✍️'\n        },\n        {\n            id: 'business',\n            name: 'Business',\n            icon: '💼'\n        },\n        {\n            id: 'technical',\n            name: 'Technical',\n            icon: '⚙️'\n        },\n        {\n            id: 'creative',\n            name: 'Creative',\n            icon: '🎨'\n        }\n    ];\n    const filteredTemplates = selectedCategory === 'all' ? templates : templates.filter((t)=>t.category === selectedCategory);\n    const loadTemplate = (template)=>{\n        // Store the template content to be loaded by the editor\n        localStorage.setItem('scroll-content', template.content);\n        // Trigger a custom event to notify the editor\n        window.dispatchEvent(new CustomEvent('loadTemplate', {\n            detail: template\n        }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-flame text-lg font-bold mb-4\",\n                children: \"\\uD83D\\uDCDC Templates\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-zinc-400 mb-2\",\n                        children: \"Categories\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-1\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedCategory(category.id),\n                                className: \"px-2 py-1 rounded text-xs transition-colors \".concat(selectedCategory === category.id ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'),\n                                children: [\n                                    category.icon,\n                                    \" \",\n                                    category.name\n                                ]\n                            }, category.id, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 space-y-2 overflow-y-auto\",\n                children: filteredTemplates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadTemplate(template),\n                        className: \"w-full text-left p-3 rounded-lg bg-zinc-800 hover:bg-zinc-700 border border-zinc-700 hover:border-zinc-600 transition-colors group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg flex-shrink-0\",\n                                    children: template.icon\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white text-sm font-medium group-hover:text-ghostblue transition-colors\",\n                                            children: template.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-zinc-400 text-xs mt-1 line-clamp-2\",\n                                            children: template.description\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this)\n                    }, template.id, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-4 border-t border-zinc-700 space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full bg-ghostblue hover:bg-ghostblue/80 text-zinc-900 py-2 px-4 rounded font-medium text-sm transition-colors\",\n                        children: \"+ New Template\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full bg-zinc-800 hover:bg-zinc-700 text-white py-2 px-4 rounded font-medium text-sm transition-colors\",\n                        children: \"\\uD83D\\uDCC1 Browse Scrolls\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(TemplateSidebar, \"ka1F1ceqEXioutdx48zEaS3nBME=\");\n_c = TemplateSidebar;\nvar _c;\n$RefreshReg$(_c, \"TemplateSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TemplateSidebar.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);