/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FThemeSelector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FThemeSelector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AnalyticsDashboard.tsx */ \"(app-pages-browser)/./src/components/AnalyticsDashboard.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScribeChatPanel.tsx */ \"(app-pages-browser)/./src/components/ScribeChatPanel.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScrollEditor.tsx */ \"(app-pages-browser)/./src/components/ScrollEditor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/TemplateSidebar.tsx */ \"(app-pages-browser)/./src/components/TemplateSidebar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeSelector.tsx */ \"(app-pages-browser)/./src/components/ThemeSelector.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FThemeSelector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!*****************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi8uLi8uLi8ubnBtL19ucHgvOTBlYjdjNzgyODZjYzA0My9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVhO0FBQ2IsS0FBcUM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsK0NBQStDLDZCQUE2QjtBQUM1RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLGdCQUFnQixnREFBZ0Q7QUFDaEUsZ0JBQWdCLGFBQWE7QUFDN0I7QUFDQTtBQUNBLGdDQUFnQyxrQ0FBa0MsT0FBTztBQUN6RTtBQUNBLGdHQUFnRyxTQUFTLFVBQVUsc0ZBQXNGLGFBQWEsVUFBVSxVQUFVO0FBQzFPO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLG1CQUFPLENBQUMsMklBQTBCO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxnQkFBZ0I7QUFDcEIsSUFBSSxjQUFjO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyIsInNvdXJjZXMiOlsiL2hvbWUvZ2hvc3QtaW4tdGhlLXdpcmUvLm5wbS9fbnB4LzkwZWI3Yzc4Mjg2Y2MwNDMvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgUmVhY3RcbiAqIHJlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qc1xuICpcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuXCJ1c2Ugc3RyaWN0XCI7XG5cInByb2R1Y3Rpb25cIiAhPT0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgJiZcbiAgKGZ1bmN0aW9uICgpIHtcbiAgICBmdW5jdGlvbiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZSkge1xuICAgICAgaWYgKG51bGwgPT0gdHlwZSkgcmV0dXJuIG51bGw7XG4gICAgICBpZiAoXCJmdW5jdGlvblwiID09PSB0eXBlb2YgdHlwZSlcbiAgICAgICAgcmV0dXJuIHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX0NMSUVOVF9SRUZFUkVOQ0VcbiAgICAgICAgICA/IG51bGxcbiAgICAgICAgICA6IHR5cGUuZGlzcGxheU5hbWUgfHwgdHlwZS5uYW1lIHx8IG51bGw7XG4gICAgICBpZiAoXCJzdHJpbmdcIiA9PT0gdHlwZW9mIHR5cGUpIHJldHVybiB0eXBlO1xuICAgICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICAgIGNhc2UgUkVBQ1RfRlJBR01FTlRfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJGcmFnbWVudFwiO1xuICAgICAgICBjYXNlIFJFQUNUX1BST0ZJTEVSX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiUHJvZmlsZXJcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9TVFJJQ1RfTU9ERV9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlN0cmljdE1vZGVcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9TVVNQRU5TRV9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlN1c3BlbnNlXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1VTUEVOU0VfTElTVF9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlN1c3BlbnNlTGlzdFwiO1xuICAgICAgICBjYXNlIFJFQUNUX0FDVElWSVRZX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiQWN0aXZpdHlcIjtcbiAgICAgIH1cbiAgICAgIGlmIChcIm9iamVjdFwiID09PSB0eXBlb2YgdHlwZSlcbiAgICAgICAgc3dpdGNoIChcbiAgICAgICAgICAoXCJudW1iZXJcIiA9PT0gdHlwZW9mIHR5cGUudGFnICYmXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgICBcIlJlY2VpdmVkIGFuIHVuZXhwZWN0ZWQgb2JqZWN0IGluIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSgpLiBUaGlzIGlzIGxpa2VseSBhIGJ1ZyBpbiBSZWFjdC4gUGxlYXNlIGZpbGUgYW4gaXNzdWUuXCJcbiAgICAgICAgICAgICksXG4gICAgICAgICAgdHlwZS4kJHR5cGVvZilcbiAgICAgICAgKSB7XG4gICAgICAgICAgY2FzZSBSRUFDVF9QT1JUQUxfVFlQRTpcbiAgICAgICAgICAgIHJldHVybiBcIlBvcnRhbFwiO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfQ09OVEVYVF9UWVBFOlxuICAgICAgICAgICAgcmV0dXJuICh0eXBlLmRpc3BsYXlOYW1lIHx8IFwiQ29udGV4dFwiKSArIFwiLlByb3ZpZGVyXCI7XG4gICAgICAgICAgY2FzZSBSRUFDVF9DT05TVU1FUl9UWVBFOlxuICAgICAgICAgICAgcmV0dXJuICh0eXBlLl9jb250ZXh0LmRpc3BsYXlOYW1lIHx8IFwiQ29udGV4dFwiKSArIFwiLkNvbnN1bWVyXCI7XG4gICAgICAgICAgY2FzZSBSRUFDVF9GT1JXQVJEX1JFRl9UWVBFOlxuICAgICAgICAgICAgdmFyIGlubmVyVHlwZSA9IHR5cGUucmVuZGVyO1xuICAgICAgICAgICAgdHlwZSA9IHR5cGUuZGlzcGxheU5hbWU7XG4gICAgICAgICAgICB0eXBlIHx8XG4gICAgICAgICAgICAgICgodHlwZSA9IGlubmVyVHlwZS5kaXNwbGF5TmFtZSB8fCBpbm5lclR5cGUubmFtZSB8fCBcIlwiKSxcbiAgICAgICAgICAgICAgKHR5cGUgPSBcIlwiICE9PSB0eXBlID8gXCJGb3J3YXJkUmVmKFwiICsgdHlwZSArIFwiKVwiIDogXCJGb3J3YXJkUmVmXCIpKTtcbiAgICAgICAgICAgIHJldHVybiB0eXBlO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfTUVNT19UWVBFOlxuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgKGlubmVyVHlwZSA9IHR5cGUuZGlzcGxheU5hbWUgfHwgbnVsbCksXG4gICAgICAgICAgICAgIG51bGwgIT09IGlubmVyVHlwZVxuICAgICAgICAgICAgICAgID8gaW5uZXJUeXBlXG4gICAgICAgICAgICAgICAgOiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZS50eXBlKSB8fCBcIk1lbW9cIlxuICAgICAgICAgICAgKTtcbiAgICAgICAgICBjYXNlIFJFQUNUX0xBWllfVFlQRTpcbiAgICAgICAgICAgIGlubmVyVHlwZSA9IHR5cGUuX3BheWxvYWQ7XG4gICAgICAgICAgICB0eXBlID0gdHlwZS5faW5pdDtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIHJldHVybiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZShpbm5lclR5cGUpKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKHgpIHt9XG4gICAgICAgIH1cbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICBmdW5jdGlvbiB0ZXN0U3RyaW5nQ29lcmNpb24odmFsdWUpIHtcbiAgICAgIHJldHVybiBcIlwiICsgdmFsdWU7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGNoZWNrS2V5U3RyaW5nQ29lcmNpb24odmFsdWUpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHRlc3RTdHJpbmdDb2VyY2lvbih2YWx1ZSk7XG4gICAgICAgIHZhciBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQgPSAhMTtcbiAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0ID0gITA7XG4gICAgICB9XG4gICAgICBpZiAoSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0KSB7XG4gICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCA9IGNvbnNvbGU7XG4gICAgICAgIHZhciBKU0NvbXBpbGVyX3RlbXBfY29uc3QgPSBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQuZXJyb3I7XG4gICAgICAgIHZhciBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQkanNjb21wJDAgPVxuICAgICAgICAgIChcImZ1bmN0aW9uXCIgPT09IHR5cGVvZiBTeW1ib2wgJiZcbiAgICAgICAgICAgIFN5bWJvbC50b1N0cmluZ1RhZyAmJlxuICAgICAgICAgICAgdmFsdWVbU3ltYm9sLnRvU3RyaW5nVGFnXSkgfHxcbiAgICAgICAgICB2YWx1ZS5jb25zdHJ1Y3Rvci5uYW1lIHx8XG4gICAgICAgICAgXCJPYmplY3RcIjtcbiAgICAgICAgSlNDb21waWxlcl90ZW1wX2NvbnN0LmNhbGwoXG4gICAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0LFxuICAgICAgICAgIFwiVGhlIHByb3ZpZGVkIGtleSBpcyBhbiB1bnN1cHBvcnRlZCB0eXBlICVzLiBUaGlzIHZhbHVlIG11c3QgYmUgY29lcmNlZCB0byBhIHN0cmluZyBiZWZvcmUgdXNpbmcgaXQgaGVyZS5cIixcbiAgICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQkanNjb21wJDBcbiAgICAgICAgKTtcbiAgICAgICAgcmV0dXJuIHRlc3RTdHJpbmdDb2VyY2lvbih2YWx1ZSk7XG4gICAgICB9XG4gICAgfVxuICAgIGZ1bmN0aW9uIGdldFRhc2tOYW1lKHR5cGUpIHtcbiAgICAgIGlmICh0eXBlID09PSBSRUFDVF9GUkFHTUVOVF9UWVBFKSByZXR1cm4gXCI8PlwiO1xuICAgICAgaWYgKFxuICAgICAgICBcIm9iamVjdFwiID09PSB0eXBlb2YgdHlwZSAmJlxuICAgICAgICBudWxsICE9PSB0eXBlICYmXG4gICAgICAgIHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX0xBWllfVFlQRVxuICAgICAgKVxuICAgICAgICByZXR1cm4gXCI8Li4uPlwiO1xuICAgICAgdHJ5IHtcbiAgICAgICAgdmFyIG5hbWUgPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZSk7XG4gICAgICAgIHJldHVybiBuYW1lID8gXCI8XCIgKyBuYW1lICsgXCI+XCIgOiBcIjwuLi4+XCI7XG4gICAgICB9IGNhdGNoICh4KSB7XG4gICAgICAgIHJldHVybiBcIjwuLi4+XCI7XG4gICAgICB9XG4gICAgfVxuICAgIGZ1bmN0aW9uIGdldE93bmVyKCkge1xuICAgICAgdmFyIGRpc3BhdGNoZXIgPSBSZWFjdFNoYXJlZEludGVybmFscy5BO1xuICAgICAgcmV0dXJuIG51bGwgPT09IGRpc3BhdGNoZXIgPyBudWxsIDogZGlzcGF0Y2hlci5nZXRPd25lcigpO1xuICAgIH1cbiAgICBmdW5jdGlvbiBVbmtub3duT3duZXIoKSB7XG4gICAgICByZXR1cm4gRXJyb3IoXCJyZWFjdC1zdGFjay10b3AtZnJhbWVcIik7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGhhc1ZhbGlkS2V5KGNvbmZpZykge1xuICAgICAgaWYgKGhhc093blByb3BlcnR5LmNhbGwoY29uZmlnLCBcImtleVwiKSkge1xuICAgICAgICB2YXIgZ2V0dGVyID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihjb25maWcsIFwia2V5XCIpLmdldDtcbiAgICAgICAgaWYgKGdldHRlciAmJiBnZXR0ZXIuaXNSZWFjdFdhcm5pbmcpIHJldHVybiAhMTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB2b2lkIDAgIT09IGNvbmZpZy5rZXk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGRlZmluZUtleVByb3BXYXJuaW5nR2V0dGVyKHByb3BzLCBkaXNwbGF5TmFtZSkge1xuICAgICAgZnVuY3Rpb24gd2FybkFib3V0QWNjZXNzaW5nS2V5KCkge1xuICAgICAgICBzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93biB8fFxuICAgICAgICAgICgoc3BlY2lhbFByb3BLZXlXYXJuaW5nU2hvd24gPSAhMCksXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgIFwiJXM6IGBrZXlgIGlzIG5vdCBhIHByb3AuIFRyeWluZyB0byBhY2Nlc3MgaXQgd2lsbCByZXN1bHQgaW4gYHVuZGVmaW5lZGAgYmVpbmcgcmV0dXJuZWQuIElmIHlvdSBuZWVkIHRvIGFjY2VzcyB0aGUgc2FtZSB2YWx1ZSB3aXRoaW4gdGhlIGNoaWxkIGNvbXBvbmVudCwgeW91IHNob3VsZCBwYXNzIGl0IGFzIGEgZGlmZmVyZW50IHByb3AuIChodHRwczovL3JlYWN0LmRldi9saW5rL3NwZWNpYWwtcHJvcHMpXCIsXG4gICAgICAgICAgICBkaXNwbGF5TmFtZVxuICAgICAgICAgICkpO1xuICAgICAgfVxuICAgICAgd2FybkFib3V0QWNjZXNzaW5nS2V5LmlzUmVhY3RXYXJuaW5nID0gITA7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocHJvcHMsIFwia2V5XCIsIHtcbiAgICAgICAgZ2V0OiB3YXJuQWJvdXRBY2Nlc3NpbmdLZXksXG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITBcbiAgICAgIH0pO1xuICAgIH1cbiAgICBmdW5jdGlvbiBlbGVtZW50UmVmR2V0dGVyV2l0aERlcHJlY2F0aW9uV2FybmluZygpIHtcbiAgICAgIHZhciBjb21wb25lbnROYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHRoaXMudHlwZSk7XG4gICAgICBkaWRXYXJuQWJvdXRFbGVtZW50UmVmW2NvbXBvbmVudE5hbWVdIHx8XG4gICAgICAgICgoZGlkV2FybkFib3V0RWxlbWVudFJlZltjb21wb25lbnROYW1lXSA9ICEwKSxcbiAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICBcIkFjY2Vzc2luZyBlbGVtZW50LnJlZiB3YXMgcmVtb3ZlZCBpbiBSZWFjdCAxOS4gcmVmIGlzIG5vdyBhIHJlZ3VsYXIgcHJvcC4gSXQgd2lsbCBiZSByZW1vdmVkIGZyb20gdGhlIEpTWCBFbGVtZW50IHR5cGUgaW4gYSBmdXR1cmUgcmVsZWFzZS5cIlxuICAgICAgICApKTtcbiAgICAgIGNvbXBvbmVudE5hbWUgPSB0aGlzLnByb3BzLnJlZjtcbiAgICAgIHJldHVybiB2b2lkIDAgIT09IGNvbXBvbmVudE5hbWUgPyBjb21wb25lbnROYW1lIDogbnVsbDtcbiAgICB9XG4gICAgZnVuY3Rpb24gUmVhY3RFbGVtZW50KFxuICAgICAgdHlwZSxcbiAgICAgIGtleSxcbiAgICAgIHNlbGYsXG4gICAgICBzb3VyY2UsXG4gICAgICBvd25lcixcbiAgICAgIHByb3BzLFxuICAgICAgZGVidWdTdGFjayxcbiAgICAgIGRlYnVnVGFza1xuICAgICkge1xuICAgICAgc2VsZiA9IHByb3BzLnJlZjtcbiAgICAgIHR5cGUgPSB7XG4gICAgICAgICQkdHlwZW9mOiBSRUFDVF9FTEVNRU5UX1RZUEUsXG4gICAgICAgIHR5cGU6IHR5cGUsXG4gICAgICAgIGtleToga2V5LFxuICAgICAgICBwcm9wczogcHJvcHMsXG4gICAgICAgIF9vd25lcjogb3duZXJcbiAgICAgIH07XG4gICAgICBudWxsICE9PSAodm9pZCAwICE9PSBzZWxmID8gc2VsZiA6IG51bGwpXG4gICAgICAgID8gT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwicmVmXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICAgICAgZ2V0OiBlbGVtZW50UmVmR2V0dGVyV2l0aERlcHJlY2F0aW9uV2FybmluZ1xuICAgICAgICAgIH0pXG4gICAgICAgIDogT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwicmVmXCIsIHsgZW51bWVyYWJsZTogITEsIHZhbHVlOiBudWxsIH0pO1xuICAgICAgdHlwZS5fc3RvcmUgPSB7fTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLl9zdG9yZSwgXCJ2YWxpZGF0ZWRcIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogMFxuICAgICAgfSk7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJfZGVidWdJbmZvXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IG51bGxcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwiX2RlYnVnU3RhY2tcIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogZGVidWdTdGFja1xuICAgICAgfSk7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJfZGVidWdUYXNrXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IGRlYnVnVGFza1xuICAgICAgfSk7XG4gICAgICBPYmplY3QuZnJlZXplICYmIChPYmplY3QuZnJlZXplKHR5cGUucHJvcHMpLCBPYmplY3QuZnJlZXplKHR5cGUpKTtcbiAgICAgIHJldHVybiB0eXBlO1xuICAgIH1cbiAgICBmdW5jdGlvbiBqc3hERVZJbXBsKFxuICAgICAgdHlwZSxcbiAgICAgIGNvbmZpZyxcbiAgICAgIG1heWJlS2V5LFxuICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgIHNvdXJjZSxcbiAgICAgIHNlbGYsXG4gICAgICBkZWJ1Z1N0YWNrLFxuICAgICAgZGVidWdUYXNrXG4gICAgKSB7XG4gICAgICB2YXIgY2hpbGRyZW4gPSBjb25maWcuY2hpbGRyZW47XG4gICAgICBpZiAodm9pZCAwICE9PSBjaGlsZHJlbilcbiAgICAgICAgaWYgKGlzU3RhdGljQ2hpbGRyZW4pXG4gICAgICAgICAgaWYgKGlzQXJyYXlJbXBsKGNoaWxkcmVuKSkge1xuICAgICAgICAgICAgZm9yIChcbiAgICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbiA9IDA7XG4gICAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4gPCBjaGlsZHJlbi5sZW5ndGg7XG4gICAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4rK1xuICAgICAgICAgICAgKVxuICAgICAgICAgICAgICB2YWxpZGF0ZUNoaWxkS2V5cyhjaGlsZHJlbltpc1N0YXRpY0NoaWxkcmVuXSk7XG4gICAgICAgICAgICBPYmplY3QuZnJlZXplICYmIE9iamVjdC5mcmVlemUoY2hpbGRyZW4pO1xuICAgICAgICAgIH0gZWxzZVxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICAgXCJSZWFjdC5qc3g6IFN0YXRpYyBjaGlsZHJlbiBzaG91bGQgYWx3YXlzIGJlIGFuIGFycmF5LiBZb3UgYXJlIGxpa2VseSBleHBsaWNpdGx5IGNhbGxpbmcgUmVhY3QuanN4cyBvciBSZWFjdC5qc3hERVYuIFVzZSB0aGUgQmFiZWwgdHJhbnNmb3JtIGluc3RlYWQuXCJcbiAgICAgICAgICAgICk7XG4gICAgICAgIGVsc2UgdmFsaWRhdGVDaGlsZEtleXMoY2hpbGRyZW4pO1xuICAgICAgaWYgKGhhc093blByb3BlcnR5LmNhbGwoY29uZmlnLCBcImtleVwiKSkge1xuICAgICAgICBjaGlsZHJlbiA9IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKTtcbiAgICAgICAgdmFyIGtleXMgPSBPYmplY3Qua2V5cyhjb25maWcpLmZpbHRlcihmdW5jdGlvbiAoaykge1xuICAgICAgICAgIHJldHVybiBcImtleVwiICE9PSBrO1xuICAgICAgICB9KTtcbiAgICAgICAgaXNTdGF0aWNDaGlsZHJlbiA9XG4gICAgICAgICAgMCA8IGtleXMubGVuZ3RoXG4gICAgICAgICAgICA/IFwie2tleTogc29tZUtleSwgXCIgKyBrZXlzLmpvaW4oXCI6IC4uLiwgXCIpICsgXCI6IC4uLn1cIlxuICAgICAgICAgICAgOiBcIntrZXk6IHNvbWVLZXl9XCI7XG4gICAgICAgIGRpZFdhcm5BYm91dEtleVNwcmVhZFtjaGlsZHJlbiArIGlzU3RhdGljQ2hpbGRyZW5dIHx8XG4gICAgICAgICAgKChrZXlzID1cbiAgICAgICAgICAgIDAgPCBrZXlzLmxlbmd0aCA/IFwie1wiICsga2V5cy5qb2luKFwiOiAuLi4sIFwiKSArIFwiOiAuLi59XCIgOiBcInt9XCIpLFxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAnQSBwcm9wcyBvYmplY3QgY29udGFpbmluZyBhIFwia2V5XCIgcHJvcCBpcyBiZWluZyBzcHJlYWQgaW50byBKU1g6XFxuICBsZXQgcHJvcHMgPSAlcztcXG4gIDwlcyB7Li4ucHJvcHN9IC8+XFxuUmVhY3Qga2V5cyBtdXN0IGJlIHBhc3NlZCBkaXJlY3RseSB0byBKU1ggd2l0aG91dCB1c2luZyBzcHJlYWQ6XFxuICBsZXQgcHJvcHMgPSAlcztcXG4gIDwlcyBrZXk9e3NvbWVLZXl9IHsuLi5wcm9wc30gLz4nLFxuICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgICAgICAgIGNoaWxkcmVuLFxuICAgICAgICAgICAga2V5cyxcbiAgICAgICAgICAgIGNoaWxkcmVuXG4gICAgICAgICAgKSxcbiAgICAgICAgICAoZGlkV2FybkFib3V0S2V5U3ByZWFkW2NoaWxkcmVuICsgaXNTdGF0aWNDaGlsZHJlbl0gPSAhMCkpO1xuICAgICAgfVxuICAgICAgY2hpbGRyZW4gPSBudWxsO1xuICAgICAgdm9pZCAwICE9PSBtYXliZUtleSAmJlxuICAgICAgICAoY2hlY2tLZXlTdHJpbmdDb2VyY2lvbihtYXliZUtleSksIChjaGlsZHJlbiA9IFwiXCIgKyBtYXliZUtleSkpO1xuICAgICAgaGFzVmFsaWRLZXkoY29uZmlnKSAmJlxuICAgICAgICAoY2hlY2tLZXlTdHJpbmdDb2VyY2lvbihjb25maWcua2V5KSwgKGNoaWxkcmVuID0gXCJcIiArIGNvbmZpZy5rZXkpKTtcbiAgICAgIGlmIChcImtleVwiIGluIGNvbmZpZykge1xuICAgICAgICBtYXliZUtleSA9IHt9O1xuICAgICAgICBmb3IgKHZhciBwcm9wTmFtZSBpbiBjb25maWcpXG4gICAgICAgICAgXCJrZXlcIiAhPT0gcHJvcE5hbWUgJiYgKG1heWJlS2V5W3Byb3BOYW1lXSA9IGNvbmZpZ1twcm9wTmFtZV0pO1xuICAgICAgfSBlbHNlIG1heWJlS2V5ID0gY29uZmlnO1xuICAgICAgY2hpbGRyZW4gJiZcbiAgICAgICAgZGVmaW5lS2V5UHJvcFdhcm5pbmdHZXR0ZXIoXG4gICAgICAgICAgbWF5YmVLZXksXG4gICAgICAgICAgXCJmdW5jdGlvblwiID09PSB0eXBlb2YgdHlwZVxuICAgICAgICAgICAgPyB0eXBlLmRpc3BsYXlOYW1lIHx8IHR5cGUubmFtZSB8fCBcIlVua25vd25cIlxuICAgICAgICAgICAgOiB0eXBlXG4gICAgICAgICk7XG4gICAgICByZXR1cm4gUmVhY3RFbGVtZW50KFxuICAgICAgICB0eXBlLFxuICAgICAgICBjaGlsZHJlbixcbiAgICAgICAgc2VsZixcbiAgICAgICAgc291cmNlLFxuICAgICAgICBnZXRPd25lcigpLFxuICAgICAgICBtYXliZUtleSxcbiAgICAgICAgZGVidWdTdGFjayxcbiAgICAgICAgZGVidWdUYXNrXG4gICAgICApO1xuICAgIH1cbiAgICBmdW5jdGlvbiB2YWxpZGF0ZUNoaWxkS2V5cyhub2RlKSB7XG4gICAgICBcIm9iamVjdFwiID09PSB0eXBlb2Ygbm9kZSAmJlxuICAgICAgICBudWxsICE9PSBub2RlICYmXG4gICAgICAgIG5vZGUuJCR0eXBlb2YgPT09IFJFQUNUX0VMRU1FTlRfVFlQRSAmJlxuICAgICAgICBub2RlLl9zdG9yZSAmJlxuICAgICAgICAobm9kZS5fc3RvcmUudmFsaWRhdGVkID0gMSk7XG4gICAgfVxuICAgIHZhciBSZWFjdCA9IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3RcIiksXG4gICAgICBSRUFDVF9FTEVNRU5UX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QudHJhbnNpdGlvbmFsLmVsZW1lbnRcIiksXG4gICAgICBSRUFDVF9QT1JUQUxfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5wb3J0YWxcIiksXG4gICAgICBSRUFDVF9GUkFHTUVOVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmZyYWdtZW50XCIpLFxuICAgICAgUkVBQ1RfU1RSSUNUX01PREVfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5zdHJpY3RfbW9kZVwiKSxcbiAgICAgIFJFQUNUX1BST0ZJTEVSX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QucHJvZmlsZXJcIik7XG4gICAgU3ltYm9sLmZvcihcInJlYWN0LnByb3ZpZGVyXCIpO1xuICAgIHZhciBSRUFDVF9DT05TVU1FUl9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmNvbnN1bWVyXCIpLFxuICAgICAgUkVBQ1RfQ09OVEVYVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmNvbnRleHRcIiksXG4gICAgICBSRUFDVF9GT1JXQVJEX1JFRl9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmZvcndhcmRfcmVmXCIpLFxuICAgICAgUkVBQ1RfU1VTUEVOU0VfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5zdXNwZW5zZVwiKSxcbiAgICAgIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5zdXNwZW5zZV9saXN0XCIpLFxuICAgICAgUkVBQ1RfTUVNT19UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0Lm1lbW9cIiksXG4gICAgICBSRUFDVF9MQVpZX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QubGF6eVwiKSxcbiAgICAgIFJFQUNUX0FDVElWSVRZX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuYWN0aXZpdHlcIiksXG4gICAgICBSRUFDVF9DTElFTlRfUkVGRVJFTkNFID0gU3ltYm9sLmZvcihcInJlYWN0LmNsaWVudC5yZWZlcmVuY2VcIiksXG4gICAgICBSZWFjdFNoYXJlZEludGVybmFscyA9XG4gICAgICAgIFJlYWN0Ll9fQ0xJRU5UX0lOVEVSTkFMU19ET19OT1RfVVNFX09SX1dBUk5fVVNFUlNfVEhFWV9DQU5OT1RfVVBHUkFERSxcbiAgICAgIGhhc093blByb3BlcnR5ID0gT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eSxcbiAgICAgIGlzQXJyYXlJbXBsID0gQXJyYXkuaXNBcnJheSxcbiAgICAgIGNyZWF0ZVRhc2sgPSBjb25zb2xlLmNyZWF0ZVRhc2tcbiAgICAgICAgPyBjb25zb2xlLmNyZWF0ZVRhc2tcbiAgICAgICAgOiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICB9O1xuICAgIFJlYWN0ID0ge1xuICAgICAgXCJyZWFjdC1zdGFjay1ib3R0b20tZnJhbWVcIjogZnVuY3Rpb24gKGNhbGxTdGFja0ZvckVycm9yKSB7XG4gICAgICAgIHJldHVybiBjYWxsU3RhY2tGb3JFcnJvcigpO1xuICAgICAgfVxuICAgIH07XG4gICAgdmFyIHNwZWNpYWxQcm9wS2V5V2FybmluZ1Nob3duO1xuICAgIHZhciBkaWRXYXJuQWJvdXRFbGVtZW50UmVmID0ge307XG4gICAgdmFyIHVua25vd25Pd25lckRlYnVnU3RhY2sgPSBSZWFjdFtcInJlYWN0LXN0YWNrLWJvdHRvbS1mcmFtZVwiXS5iaW5kKFxuICAgICAgUmVhY3QsXG4gICAgICBVbmtub3duT3duZXJcbiAgICApKCk7XG4gICAgdmFyIHVua25vd25Pd25lckRlYnVnVGFzayA9IGNyZWF0ZVRhc2soZ2V0VGFza05hbWUoVW5rbm93bk93bmVyKSk7XG4gICAgdmFyIGRpZFdhcm5BYm91dEtleVNwcmVhZCA9IHt9O1xuICAgIGV4cG9ydHMuRnJhZ21lbnQgPSBSRUFDVF9GUkFHTUVOVF9UWVBFO1xuICAgIGV4cG9ydHMuanN4REVWID0gZnVuY3Rpb24gKFxuICAgICAgdHlwZSxcbiAgICAgIGNvbmZpZyxcbiAgICAgIG1heWJlS2V5LFxuICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgIHNvdXJjZSxcbiAgICAgIHNlbGZcbiAgICApIHtcbiAgICAgIHZhciB0cmFja0FjdHVhbE93bmVyID1cbiAgICAgICAgMWU0ID4gUmVhY3RTaGFyZWRJbnRlcm5hbHMucmVjZW50bHlDcmVhdGVkT3duZXJTdGFja3MrKztcbiAgICAgIHJldHVybiBqc3hERVZJbXBsKFxuICAgICAgICB0eXBlLFxuICAgICAgICBjb25maWcsXG4gICAgICAgIG1heWJlS2V5LFxuICAgICAgICBpc1N0YXRpY0NoaWxkcmVuLFxuICAgICAgICBzb3VyY2UsXG4gICAgICAgIHNlbGYsXG4gICAgICAgIHRyYWNrQWN0dWFsT3duZXJcbiAgICAgICAgICA/IEVycm9yKFwicmVhY3Qtc3RhY2stdG9wLWZyYW1lXCIpXG4gICAgICAgICAgOiB1bmtub3duT3duZXJEZWJ1Z1N0YWNrLFxuICAgICAgICB0cmFja0FjdHVhbE93bmVyID8gY3JlYXRlVGFzayhnZXRUYXNrTmFtZSh0eXBlKSkgOiB1bmtub3duT3duZXJEZWJ1Z1Rhc2tcbiAgICAgICk7XG4gICAgfTtcbiAgfSkoKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!*******************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \*******************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi8uLi8uLi8ubnBtL19ucHgvOTBlYjdjNzgyODZjYzA0My9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixJQUFJLEtBQXFDLEVBQUUsRUFFMUMsQ0FBQztBQUNGLEVBQUUsbU9BQXNFO0FBQ3hFIiwic291cmNlcyI6WyIvaG9tZS9naG9zdC1pbi10aGUtd2lyZS8ubnBtL19ucHgvOTBlYjdjNzgyODZjYzA0My9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AnalyticsDashboard.tsx":
/*!***********************************************!*\
  !*** ./src/components/AnalyticsDashboard.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction AnalyticsDashboard() {\n    _s();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentSession, setCurrentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsDashboard.useEffect\": ()=>{\n            // Load analytics data from localStorage\n            loadAnalyticsData();\n            // Start tracking current session\n            startSession();\n            // Update session every minute\n            const interval = setInterval(updateCurrentSession, 60000);\n            return ({\n                \"AnalyticsDashboard.useEffect\": ()=>{\n                    clearInterval(interval);\n                    endSession();\n                }\n            })[\"AnalyticsDashboard.useEffect\"];\n        }\n    }[\"AnalyticsDashboard.useEffect\"], []);\n    const loadAnalyticsData = ()=>{\n        try {\n            const stored = localStorage.getItem('writeos-analytics');\n            if (stored) {\n                setAnalyticsData(JSON.parse(stored));\n            } else {\n                // Initialize with default data\n                const defaultData = {\n                    totalSessions: 0,\n                    totalWordsWritten: 0,\n                    totalTimeSpent: 0,\n                    averageWordsPerSession: 0,\n                    averageSessionLength: 0,\n                    mostProductiveHour: 14,\n                    weeklyProgress: [\n                        0,\n                        0,\n                        0,\n                        0,\n                        0,\n                        0,\n                        0\n                    ],\n                    aiUsageStats: {\n                        enhanceCount: 0,\n                        summarizeCount: 0,\n                        analyzeCount: 0,\n                        chatMessages: 0\n                    }\n                };\n                setAnalyticsData(defaultData);\n                localStorage.setItem('writeos-analytics', JSON.stringify(defaultData));\n            }\n        } catch (error) {\n            console.error('Error loading analytics data:', error);\n        }\n    };\n    const startSession = ()=>{\n        const session = {\n            id: Date.now().toString(),\n            startTime: new Date(),\n            wordCount: 0,\n            charactersTyped: 0,\n            timeSpent: 0,\n            documentsCreated: 0,\n            aiInteractions: 0\n        };\n        setCurrentSession(session);\n    };\n    const updateCurrentSession = ()=>{\n        if (currentSession) {\n            const now = new Date();\n            const timeSpent = Math.floor((now.getTime() - currentSession.startTime.getTime()) / 60000);\n            setCurrentSession((prev)=>prev ? {\n                    ...prev,\n                    timeSpent\n                } : null);\n        }\n    };\n    const endSession = ()=>{\n        if (currentSession && analyticsData) {\n            const updatedData = {\n                ...analyticsData,\n                totalSessions: analyticsData.totalSessions + 1,\n                totalTimeSpent: analyticsData.totalTimeSpent + currentSession.timeSpent,\n                totalWordsWritten: analyticsData.totalWordsWritten + currentSession.wordCount,\n                averageWordsPerSession: Math.round((analyticsData.totalWordsWritten + currentSession.wordCount) / (analyticsData.totalSessions + 1)),\n                averageSessionLength: Math.round((analyticsData.totalTimeSpent + currentSession.timeSpent) / (analyticsData.totalSessions + 1))\n            };\n            setAnalyticsData(updatedData);\n            localStorage.setItem('writeos-analytics', JSON.stringify(updatedData));\n        }\n    };\n    const formatTime = (minutes)=>{\n        const hours = Math.floor(minutes / 60);\n        const mins = minutes % 60;\n        return hours > 0 ? \"\".concat(hours, \"h \").concat(mins, \"m\") : \"\".concat(mins, \"m\");\n    };\n    const getProductivityLevel = ()=>{\n        if (!currentSession) return 'Starting';\n        if (currentSession.timeSpent < 5) return 'Warming up';\n        if (currentSession.timeSpent < 15) return 'Getting focused';\n        if (currentSession.timeSpent < 30) return 'In the zone';\n        if (currentSession.timeSpent < 60) return 'Deep work';\n        return 'Marathon session!';\n    };\n    if (!analyticsData) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsVisible(!isVisible),\n                className: \"bg-flame hover:bg-flame/80 text-white p-3 rounded-full shadow-lg transition-all duration-300 mb-2\",\n                children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-zinc-900 border border-zinc-700 rounded-lg p-4 w-80 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-flame font-bold text-lg\",\n                                children: \"\\uD83D\\uDCCA Writing Analytics\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsVisible(false),\n                                className: \"text-zinc-400 hover:text-white\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-3 bg-zinc-800 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-ghostblue font-semibold mb-2\",\n                                children: \"Current Session\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-zinc-400\",\n                                                children: \"Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: getProductivityLevel()\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-zinc-400\",\n                                                children: \"Time:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: formatTime((currentSession === null || currentSession === void 0 ? void 0 : currentSession.timeSpent) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-zinc-400\",\n                                                children: \"Words:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.wordCount) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-zinc-800 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-flame\",\n                                                children: analyticsData.totalSessions\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-zinc-400\",\n                                                children: \"Total Sessions\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-zinc-800 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-ghostblue\",\n                                                children: analyticsData.totalWordsWritten.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-zinc-400\",\n                                                children: \"Words Written\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-zinc-800 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: formatTime(analyticsData.totalTimeSpent)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-zinc-400\",\n                                                children: \"Time Spent\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-zinc-800 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: analyticsData.averageWordsPerSession\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-zinc-400\",\n                                                children: \"Avg Words/Session\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-zinc-800 p-3 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"text-ghostblue font-semibold mb-2 text-sm\",\n                                        children: \"AI Assistance\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Enhance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: analyticsData.aiUsageStats.enhanceCount\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Analyze:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: analyticsData.aiUsageStats.analyzeCount\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Summarize:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: analyticsData.aiUsageStats.summarizeCount\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Chat:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: analyticsData.aiUsageStats.chatMessages\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-zinc-800 p-3 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"text-flame font-semibold mb-2 text-sm\",\n                                        children: \"\\uD83D\\uDCA1 Insights\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-zinc-300 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• Most productive at \",\n                                                    analyticsData.mostProductiveHour,\n                                                    \":00\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• Average session: \",\n                                                    formatTime(analyticsData.averageSessionLength)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• \",\n                                                    analyticsData.totalWordsWritten > 1000 ? 'Great progress!' : 'Keep writing!'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsDashboard, \"RvSlZLiCor9zPeJWLgWRWd8cFn0=\");\n_c = AnalyticsDashboard;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AnalyticsDashboard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ScribeChatPanel.tsx":
/*!********************************************!*\
  !*** ./src/components/ScribeChatPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScribeChatPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ScribeChatPanel() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            role: 'assistant',\n            content: 'Welcome to your AI writing assistant. How can I help you craft your scroll today?',\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScribeChatPanel.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ScribeChatPanel.useEffect\"], [\n        messages\n    ]);\n    const sendMessage = async ()=>{\n        if (!input.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: input.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput('');\n        setIsLoading(true);\n        try {\n            // Phase III - Real AI Integration\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    history: messages.slice(-5),\n                    context: 'writing-assistant'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                const aiResponse = {\n                    id: (Date.now() + 1).toString(),\n                    role: 'assistant',\n                    content: result.message,\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiResponse\n                    ]);\n                // Update analytics\n                updateAnalytics('chatMessage');\n            } else {\n                // Fallback to local response\n                const aiResponse = {\n                    id: (Date.now() + 1).toString(),\n                    role: 'assistant',\n                    content: generateAIResponse(userMessage.content),\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiResponse\n                    ]);\n            }\n        } catch (error) {\n            console.error('Error sending message:', error);\n            // Fallback response\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                role: 'assistant',\n                content: \"I apologize, but I'm having trouble connecting right now. Please try again in a moment.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiResponse\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateAnalytics = (action)=>{\n        try {\n            const stored = localStorage.getItem('writeos-analytics');\n            if (stored) {\n                const analytics = JSON.parse(stored);\n                if (action === 'chatMessage') {\n                    analytics.aiUsageStats.chatMessages += 1;\n                }\n                localStorage.setItem('writeos-analytics', JSON.stringify(analytics));\n            }\n        } catch (error) {\n            console.error('Error updating analytics:', error);\n        }\n    };\n    const generateAIResponse = (userInput)=>{\n        // Enhanced AI response logic\n        const responses = {\n            writing: [\n                \"I can help you structure your thoughts. What type of document are you working on?\",\n                \"Let's break this down into clear sections. What's your main objective?\",\n                \"Consider starting with an outline. What are the key points you want to cover?\"\n            ],\n            editing: [\n                \"I can help refine your prose. Share a paragraph and I'll suggest improvements.\",\n                \"Let's focus on clarity and flow. What section needs the most work?\",\n                \"Consider your audience - who are you writing for?\"\n            ],\n            ideas: [\n                \"Let's brainstorm! What's the core concept you want to explore?\",\n                \"I can help generate creative angles. What's your starting point?\",\n                \"What if we approached this from a different perspective?\"\n            ],\n            default: [\n                \"I'm here to help with your writing. What would you like to work on?\",\n                \"Let's craft something amazing together. What's on your mind?\",\n                \"I can assist with writing, editing, brainstorming, or structuring. How can I help?\"\n            ]\n        };\n        const input = userInput.toLowerCase();\n        if (input.includes('write') || input.includes('draft')) {\n            return responses.writing[Math.floor(Math.random() * responses.writing.length)];\n        } else if (input.includes('edit') || input.includes('improve') || input.includes('fix')) {\n            return responses.editing[Math.floor(Math.random() * responses.editing.length)];\n        } else if (input.includes('idea') || input.includes('brainstorm') || input.includes('think')) {\n            return responses.ideas[Math.floor(Math.random() * responses.ideas.length)];\n        } else {\n            return responses.default[Math.floor(Math.random() * responses.default.length)];\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-flame text-lg font-bold mb-4\",\n                children: \"\\uD83E\\uDD16 Scribe Assistant\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg p-4 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto mb-4 space-y-3\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-[80%] p-3 rounded-lg text-sm \".concat(message.role === 'user' ? 'bg-flame text-white' : 'bg-zinc-700 text-zinc-100'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-60 mt-1\",\n                                                children: message.timestamp.toLocaleTimeString([], {\n                                                    hour: '2-digit',\n                                                    minute: '2-digit'\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                }, message.id, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-zinc-700 text-zinc-100 p-3 rounded-lg text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Thinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                className: \"flex-1 bg-zinc-800 border border-zinc-700 rounded px-3 py-2 text-white text-sm focus:outline-none focus:border-ghostblue\",\n                                placeholder: \"Ask your scribe assistant...\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: sendMessage,\n                                disabled: !input.trim() || isLoading,\n                                className: \"bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded text-sm font-medium transition-colors\",\n                                children: \"Send\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n_s(ScribeChatPanel, \"a/3GhHbUQ1u1bp48TjjmheS6EE4=\");\n_c = ScribeChatPanel;\nvar _c;\n$RefreshReg$(_c, \"ScribeChatPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ScribeChatPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ScrollEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/ScrollEditor.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ScrollEditor() {\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isPreview, setIsPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            if (content.trim()) {\n                const timer = setTimeout({\n                    \"ScrollEditor.useEffect.timer\": ()=>{\n                        // Simulate auto-save\n                        localStorage.setItem('scroll-content', content);\n                        setLastSaved(new Date());\n                    }\n                }[\"ScrollEditor.useEffect.timer\"], 2000);\n                return ({\n                    \"ScrollEditor.useEffect\": ()=>clearTimeout(timer)\n                })[\"ScrollEditor.useEffect\"];\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], [\n        content\n    ]);\n    // Load saved content on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const saved = localStorage.getItem('scroll-content');\n            if (saved) {\n                setContent(saved);\n                setLastSaved(new Date());\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Listen for template loading events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const handleLoadTemplate = {\n                \"ScrollEditor.useEffect.handleLoadTemplate\": (event)=>{\n                    const template = event.detail;\n                    setContent(template.content);\n                    setLastSaved(null); // Reset save status for new template\n                    // Focus the editor\n                    setTimeout({\n                        \"ScrollEditor.useEffect.handleLoadTemplate\": ()=>{\n                            var _textareaRef_current;\n                            (_textareaRef_current = textareaRef.current) === null || _textareaRef_current === void 0 ? void 0 : _textareaRef_current.focus();\n                        }\n                    }[\"ScrollEditor.useEffect.handleLoadTemplate\"], 100);\n                }\n            }[\"ScrollEditor.useEffect.handleLoadTemplate\"];\n            window.addEventListener('loadTemplate', handleLoadTemplate);\n            return ({\n                \"ScrollEditor.useEffect\": ()=>{\n                    window.removeEventListener('loadTemplate', handleLoadTemplate);\n                }\n            })[\"ScrollEditor.useEffect\"];\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Calculate stats\n    const wordCount = content.trim() ? content.trim().split(/\\s+/).length : 0;\n    const charCount = content.length;\n    const lineCount = content.split('\\n').length;\n    // Simple markdown preview (basic implementation)\n    const renderMarkdown = (text)=>{\n        return text.replace(/^# (.*$)/gim, '<h1 class=\"text-2xl font-bold text-ghostblue mb-4\">$1</h1>').replace(/^## (.*$)/gim, '<h2 class=\"text-xl font-bold text-white mb-3\">$1</h2>').replace(/^### (.*$)/gim, '<h3 class=\"text-lg font-bold text-zinc-300 mb-2\">$1</h3>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong class=\"font-bold text-white\">$1</strong>').replace(/\\*(.*?)\\*/g, '<em class=\"italic text-zinc-300\">$1</em>').replace(/`(.*?)`/g, '<code class=\"bg-zinc-800 text-flame px-1 rounded\">$1</code>').replace(/\\n/g, '<br>');\n    };\n    const handleKeyDown = (e)=>{\n        // Tab support\n        if (e.key === 'Tab') {\n            e.preventDefault();\n            const start = e.currentTarget.selectionStart;\n            const end = e.currentTarget.selectionEnd;\n            const newContent = content.substring(0, start) + '  ' + content.substring(end);\n            setContent(newContent);\n            // Restore cursor position\n            setTimeout(()=>{\n                if (textareaRef.current) {\n                    textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 2;\n                }\n            }, 0);\n        }\n    };\n    const insertTemplate = (template)=>{\n        const templates = {\n            heading: '# Your Heading Here\\n\\n',\n            list: '- Item 1\\n- Item 2\\n- Item 3\\n\\n',\n            code: '```\\nYour code here\\n```\\n\\n',\n            quote: '> Your quote here\\n\\n',\n            table: '| Column 1 | Column 2 |\\n|----------|----------|\\n| Cell 1   | Cell 2   |\\n\\n'\n        };\n        const insertion = templates[template] || '';\n        const textarea = textareaRef.current;\n        if (textarea) {\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const newContent = content.substring(0, start) + insertion + content.substring(end);\n            setContent(newContent);\n            // Focus and position cursor\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.selectionStart = textarea.selectionEnd = start + insertion.length;\n            }, 0);\n        }\n    };\n    const handleAscend = async (action)=>{\n        if (!content.trim() || isProcessing) return;\n        setIsProcessing(true);\n        try {\n            const response = await fetch('/api/ascend', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content,\n                    action,\n                    context: 'scroll-editor'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                var _result_metadata;\n                setContent(result.processedContent);\n                setLastSaved(null); // Reset save status\n                // Update analytics\n                updateAnalytics(action);\n                // Show success notification with AI provider info\n                console.log('Ascend completed:', result.message);\n                console.log('AI Provider:', result.aiProvider || 'Fallback System');\n                console.log('Processing Time:', ((_result_metadata = result.metadata) === null || _result_metadata === void 0 ? void 0 : _result_metadata.processingTime) || 'N/A');\n                console.log('Suggestions:', result.suggestions);\n                // You could add a toast notification here\n                showNotification(\"\".concat(action, \" completed successfully!\"), 'success');\n            } else {\n                console.error('Ascend failed:', result.error);\n                showNotification(\"\".concat(action, \" failed. Please try again.\"), 'error');\n            }\n        } catch (error) {\n            console.error('Ascend error:', error);\n            showNotification('Connection error. Please check your internet.', 'error');\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    const updateAnalytics = (action)=>{\n        try {\n            const stored = localStorage.getItem('writeos-analytics');\n            if (stored) {\n                const analytics = JSON.parse(stored);\n                switch(action){\n                    case 'enhance':\n                        analytics.aiUsageStats.enhanceCount += 1;\n                        break;\n                    case 'summarize':\n                        analytics.aiUsageStats.summarizeCount += 1;\n                        break;\n                    case 'analyze':\n                        analytics.aiUsageStats.analyzeCount += 1;\n                        break;\n                }\n                localStorage.setItem('writeos-analytics', JSON.stringify(analytics));\n            }\n        } catch (error) {\n            console.error('Error updating analytics:', error);\n        }\n    };\n    const showNotification = (message, type)=>{\n        // Simple notification system - you could enhance this with a proper toast library\n        console.log(\"[\".concat(type.toUpperCase(), \"] \").concat(message));\n        // Create a temporary visual notification\n        const notification = document.createElement('div');\n        notification.className = \"fixed top-4 right-4 p-3 rounded-lg text-white z-50 \".concat(type === 'success' ? 'bg-green-600' : 'bg-red-600');\n        notification.textContent = message;\n        document.body.appendChild(notification);\n        setTimeout(()=>{\n            document.body.removeChild(notification);\n        }, 3000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-ghostblue text-lg font-bold\",\n                        children: \"✍️ Scroll Editor\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsPreview(!isPreview),\n                                className: \"px-3 py-1 rounded text-xs font-medium transition-colors \".concat(isPreview ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-700 text-white hover:bg-zinc-600'),\n                                children: isPreview ? '📝 Edit' : '👁️ Preview'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    '🔥 Enhance',\n                                    '📋 Summarize',\n                                    '🎨 Format',\n                                    '📊 Analyze'\n                                ].map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleAscend(action.split(' ')[1].toLowerCase()),\n                                        className: \"px-2 py-1 bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white text-xs rounded font-medium transition-colors\",\n                                        disabled: !content.trim() || isProcessing,\n                                        children: isProcessing ? '⏳' : action\n                                    }, action, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-zinc-500\",\n                                children: lastSaved ? \"Saved \".concat(lastSaved.toLocaleTimeString()) : 'Not saved'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-1 mb-3\",\n                children: [\n                    {\n                        label: 'H1',\n                        action: ()=>insertTemplate('heading')\n                    },\n                    {\n                        label: 'List',\n                        action: ()=>insertTemplate('list')\n                    },\n                    {\n                        label: 'Code',\n                        action: ()=>insertTemplate('code')\n                    },\n                    {\n                        label: 'Quote',\n                        action: ()=>insertTemplate('quote')\n                    },\n                    {\n                        label: 'Table',\n                        action: ()=>insertTemplate('table')\n                    }\n                ].map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: tool.action,\n                        className: \"px-2 py-1 bg-zinc-800 hover:bg-zinc-700 text-zinc-300 text-xs rounded transition-colors\",\n                        children: tool.label\n                    }, tool.label, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg overflow-hidden\",\n                children: isPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-4 overflow-y-auto prose prose-invert max-w-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white leading-relaxed\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(content)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: content,\n                    onChange: (e)=>setContent(e.target.value),\n                    onKeyDown: handleKeyDown,\n                    className: \"w-full h-full bg-transparent text-white resize-none outline-none p-4 font-mono text-sm leading-relaxed\",\n                    placeholder: \"Begin your scroll here...  # Welcome to your Scroll Editor  Start writing in **Markdown** format: - Use # for headings - Use **bold** and *italic* text - Create `code snippets` - Make lists and more!  Press Tab for indentation, Ctrl+Enter for preview.\",\n                    spellCheck: false\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 flex items-center justify-between text-xs text-zinc-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Words: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: wordCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Characters: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: charCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Lines: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: lineCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            content.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-ghostblue rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Ready\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n_s(ScrollEditor, \"RY4gjijf6zMgAVQrPlad4F9++uo=\");\n_c = ScrollEditor;\nvar _c;\n$RefreshReg$(_c, \"ScrollEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ScrollEditor.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/TemplateSidebar.tsx":
/*!********************************************!*\
  !*** ./src/components/TemplateSidebar.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TemplateSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction TemplateSidebar() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const templates = [\n        {\n            id: '1',\n            name: \"Blank Scroll\",\n            icon: \"📜\",\n            content: \"# New Document\\n\\nStart writing here...\",\n            description: \"Empty document to start fresh\",\n            category: 'writing'\n        },\n        {\n            id: '2',\n            name: \"Technical Doc\",\n            icon: \"⚙️\",\n            content: \"# Technical Documentation\\n\\n## Overview\\n\\n## Requirements\\n\\n## Implementation\\n\\n## Testing\\n\\n## Deployment\",\n            description: \"Structure for technical documentation\",\n            category: 'technical'\n        },\n        {\n            id: '3',\n            name: \"Creative Writing\",\n            icon: \"✨\",\n            content: \"# Story Title\\n\\n*Genre: [Your Genre]*\\n\\n## Characters\\n\\n## Plot Outline\\n\\n## Chapter 1\\n\\nOnce upon a time...\",\n            description: \"Template for creative stories\",\n            category: 'creative'\n        },\n        {\n            id: '4',\n            name: \"Meeting Notes\",\n            icon: \"📝\",\n            content: \"# Meeting Notes\\n\\n**Date:** [Date]\\n**Attendees:** [Names]\\n**Agenda:**\\n\\n## Discussion Points\\n\\n## Action Items\\n\\n## Next Steps\",\n            description: \"Structured meeting documentation\",\n            category: 'business'\n        },\n        {\n            id: '5',\n            name: \"Project Plan\",\n            icon: \"🎯\",\n            content: \"# Project Plan\\n\\n## Objective\\n\\n## Scope\\n\\n## Timeline\\n\\n## Resources\\n\\n## Milestones\\n\\n## Risk Assessment\",\n            description: \"Comprehensive project planning\",\n            category: 'business'\n        },\n        {\n            id: '6',\n            name: \"Blog Post\",\n            icon: \"📰\",\n            content: \"# Blog Post Title\\n\\n*Published: [Date]*\\n*Tags: [tag1, tag2]*\\n\\n## Introduction\\n\\n## Main Content\\n\\n## Conclusion\\n\\n---\\n*What do you think? Leave a comment below!*\",\n            description: \"Blog post structure\",\n            category: 'writing'\n        },\n        {\n            id: '7',\n            name: \"API Documentation\",\n            icon: \"🔌\",\n            content: \"# API Documentation\\n\\n## Endpoints\\n\\n### GET /api/endpoint\\n\\n**Description:** \\n\\n**Parameters:**\\n\\n**Response:**\\n\\n```json\\n{\\n  \\\"status\\\": \\\"success\\\"\\n}\\n```\",\n            description: \"API reference template\",\n            category: 'technical'\n        }\n    ];\n    const categories = [\n        {\n            id: 'all',\n            name: 'All',\n            icon: '📚'\n        },\n        {\n            id: 'writing',\n            name: 'Writing',\n            icon: '✍️'\n        },\n        {\n            id: 'business',\n            name: 'Business',\n            icon: '💼'\n        },\n        {\n            id: 'technical',\n            name: 'Technical',\n            icon: '⚙️'\n        },\n        {\n            id: 'creative',\n            name: 'Creative',\n            icon: '🎨'\n        }\n    ];\n    const filteredTemplates = selectedCategory === 'all' ? templates : templates.filter((t)=>t.category === selectedCategory);\n    const loadTemplate = (template)=>{\n        // Store the template content to be loaded by the editor\n        localStorage.setItem('scroll-content', template.content);\n        // Trigger a custom event to notify the editor\n        window.dispatchEvent(new CustomEvent('loadTemplate', {\n            detail: template\n        }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-flame text-lg font-bold mb-4\",\n                children: \"\\uD83D\\uDCDC Templates\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-zinc-400 mb-2\",\n                        children: \"Categories\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-1\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedCategory(category.id),\n                                className: \"px-2 py-1 rounded text-xs transition-colors \".concat(selectedCategory === category.id ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'),\n                                children: [\n                                    category.icon,\n                                    \" \",\n                                    category.name\n                                ]\n                            }, category.id, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 space-y-2 overflow-y-auto\",\n                children: filteredTemplates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadTemplate(template),\n                        className: \"w-full text-left p-3 rounded-lg bg-zinc-800 hover:bg-zinc-700 border border-zinc-700 hover:border-zinc-600 transition-colors group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg flex-shrink-0\",\n                                    children: template.icon\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white text-sm font-medium group-hover:text-ghostblue transition-colors\",\n                                            children: template.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-zinc-400 text-xs mt-1 line-clamp-2\",\n                                            children: template.description\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this)\n                    }, template.id, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-4 border-t border-zinc-700 space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full bg-ghostblue hover:bg-ghostblue/80 text-zinc-900 py-2 px-4 rounded font-medium text-sm transition-colors\",\n                        children: \"+ New Template\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full bg-zinc-800 hover:bg-zinc-700 text-white py-2 px-4 rounded font-medium text-sm transition-colors\",\n                        children: \"\\uD83D\\uDCC1 Browse Scrolls\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(TemplateSidebar, \"ka1F1ceqEXioutdx48zEaS3nBME=\");\n_c = TemplateSidebar;\nvar _c;\n$RefreshReg$(_c, \"TemplateSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1RlbXBsYXRlU2lkZWJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWlDO0FBV2xCLFNBQVNDOztJQUN0QixNQUFNLENBQUNDLGtCQUFrQkMsb0JBQW9CLEdBQUdILCtDQUFRQSxDQUFTO0lBRWpFLE1BQU1JLFlBQXdCO1FBQzVCO1lBQ0VDLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFNBQVM7WUFDVEMsYUFBYTtZQUNiQyxVQUFVO1FBQ1o7UUFDQTtZQUNFTCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxTQUFTO1lBQ1RDLGFBQWE7WUFDYkMsVUFBVTtRQUNaO1FBQ0E7WUFDRUwsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsU0FBUztZQUNUQyxhQUFhO1lBQ2JDLFVBQVU7UUFDWjtRQUNBO1lBQ0VMLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFNBQVM7WUFDVEMsYUFBYTtZQUNiQyxVQUFVO1FBQ1o7UUFDQTtZQUNFTCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxTQUFTO1lBQ1RDLGFBQWE7WUFDYkMsVUFBVTtRQUNaO1FBQ0E7WUFDRUwsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsU0FBUztZQUNUQyxhQUFhO1lBQ2JDLFVBQVU7UUFDWjtRQUNBO1lBQ0VMLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFNBQVM7WUFDVEMsYUFBYTtZQUNiQyxVQUFVO1FBQ1o7S0FDRDtJQUVELE1BQU1DLGFBQWE7UUFDakI7WUFBRU4sSUFBSTtZQUFPQyxNQUFNO1lBQU9DLE1BQU07UUFBSztRQUNyQztZQUFFRixJQUFJO1lBQVdDLE1BQU07WUFBV0MsTUFBTTtRQUFLO1FBQzdDO1lBQUVGLElBQUk7WUFBWUMsTUFBTTtZQUFZQyxNQUFNO1FBQUs7UUFDL0M7WUFBRUYsSUFBSTtZQUFhQyxNQUFNO1lBQWFDLE1BQU07UUFBSztRQUNqRDtZQUFFRixJQUFJO1lBQVlDLE1BQU07WUFBWUMsTUFBTTtRQUFLO0tBQ2hEO0lBRUQsTUFBTUssb0JBQW9CVixxQkFBcUIsUUFDM0NFLFlBQ0FBLFVBQVVTLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUosUUFBUSxLQUFLUjtJQUV6QyxNQUFNYSxlQUFlLENBQUNDO1FBQ3BCLHdEQUF3RDtRQUN4REMsYUFBYUMsT0FBTyxDQUFDLGtCQUFrQkYsU0FBU1IsT0FBTztRQUN2RCw4Q0FBOEM7UUFDOUNXLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLGdCQUFnQjtZQUFFQyxRQUFRTjtRQUFTO0lBQzFFO0lBRUEscUJBQ0UsOERBQUNPO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFBb0M7Ozs7OzswQkFHbkQsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQTZCOzs7Ozs7a0NBQzVDLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWmIsV0FBV2MsR0FBRyxDQUFDLENBQUNmLHlCQUNmLDhEQUFDZ0I7Z0NBRUNDLFNBQVMsSUFBTXhCLG9CQUFvQk8sU0FBU0wsRUFBRTtnQ0FDOUNtQixXQUFXLCtDQUlWLE9BSEN0QixxQkFBcUJRLFNBQVNMLEVBQUUsR0FDNUIsK0JBQ0E7O29DQUdMSyxTQUFTSCxJQUFJO29DQUFDO29DQUFFRyxTQUFTSixJQUFJOzsrQkFSekJJLFNBQVNMLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBZXhCLDhEQUFDa0I7Z0JBQUlDLFdBQVU7MEJBQ1paLGtCQUFrQmEsR0FBRyxDQUFDLENBQUNULHlCQUN0Qiw4REFBQ1U7d0JBRUNDLFNBQVMsSUFBTVosYUFBYUM7d0JBQzVCUSxXQUFVO2tDQUVWLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNJO29DQUFLSixXQUFVOzhDQUF5QlIsU0FBU1QsSUFBSTs7Ozs7OzhDQUN0RCw4REFBQ2dCO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ1pSLFNBQVNWLElBQUk7Ozs7OztzREFFaEIsOERBQUNpQjs0Q0FBSUMsV0FBVTtzREFDWlIsU0FBU1AsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VCQVh0Qk8sU0FBU1gsRUFBRTs7Ozs7Ozs7OzswQkFvQnRCLDhEQUFDa0I7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRTt3QkFBT0YsV0FBVTtrQ0FBa0g7Ozs7OztrQ0FHcEksOERBQUNFO3dCQUFPRixXQUFVO2tDQUEwRzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXBJO0dBM0l3QnZCO0tBQUFBIiwic291cmNlcyI6WyIvaG9tZS9naG9zdC1pbi10aGUtd2lyZS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy93cml0ZW9zLXNjcmliZS10ZXJtaW5hbC93cml0ZW9zLXNjcmliZS10ZXJtaW5hbC9zcmMvY29tcG9uZW50cy9UZW1wbGF0ZVNpZGViYXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIFRlbXBsYXRlIHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBpY29uOiBzdHJpbmc7XG4gIGNvbnRlbnQ6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgY2F0ZWdvcnk6ICd3cml0aW5nJyB8ICdidXNpbmVzcycgfCAndGVjaG5pY2FsJyB8ICdjcmVhdGl2ZSc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFRlbXBsYXRlU2lkZWJhcigpIHtcbiAgY29uc3QgW3NlbGVjdGVkQ2F0ZWdvcnksIHNldFNlbGVjdGVkQ2F0ZWdvcnldID0gdXNlU3RhdGU8c3RyaW5nPignYWxsJyk7XG5cbiAgY29uc3QgdGVtcGxhdGVzOiBUZW1wbGF0ZVtdID0gW1xuICAgIHtcbiAgICAgIGlkOiAnMScsXG4gICAgICBuYW1lOiBcIkJsYW5rIFNjcm9sbFwiLFxuICAgICAgaWNvbjogXCLwn5OcXCIsXG4gICAgICBjb250ZW50OiBcIiMgTmV3IERvY3VtZW50XFxuXFxuU3RhcnQgd3JpdGluZyBoZXJlLi4uXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJFbXB0eSBkb2N1bWVudCB0byBzdGFydCBmcmVzaFwiLFxuICAgICAgY2F0ZWdvcnk6ICd3cml0aW5nJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICcyJyxcbiAgICAgIG5hbWU6IFwiVGVjaG5pY2FsIERvY1wiLFxuICAgICAgaWNvbjogXCLimpnvuI9cIixcbiAgICAgIGNvbnRlbnQ6IFwiIyBUZWNobmljYWwgRG9jdW1lbnRhdGlvblxcblxcbiMjIE92ZXJ2aWV3XFxuXFxuIyMgUmVxdWlyZW1lbnRzXFxuXFxuIyMgSW1wbGVtZW50YXRpb25cXG5cXG4jIyBUZXN0aW5nXFxuXFxuIyMgRGVwbG95bWVudFwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiU3RydWN0dXJlIGZvciB0ZWNobmljYWwgZG9jdW1lbnRhdGlvblwiLFxuICAgICAgY2F0ZWdvcnk6ICd0ZWNobmljYWwnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJzMnLFxuICAgICAgbmFtZTogXCJDcmVhdGl2ZSBXcml0aW5nXCIsXG4gICAgICBpY29uOiBcIuKcqFwiLFxuICAgICAgY29udGVudDogXCIjIFN0b3J5IFRpdGxlXFxuXFxuKkdlbnJlOiBbWW91ciBHZW5yZV0qXFxuXFxuIyMgQ2hhcmFjdGVyc1xcblxcbiMjIFBsb3QgT3V0bGluZVxcblxcbiMjIENoYXB0ZXIgMVxcblxcbk9uY2UgdXBvbiBhIHRpbWUuLi5cIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlRlbXBsYXRlIGZvciBjcmVhdGl2ZSBzdG9yaWVzXCIsXG4gICAgICBjYXRlZ29yeTogJ2NyZWF0aXZlJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICc0JyxcbiAgICAgIG5hbWU6IFwiTWVldGluZyBOb3Rlc1wiLFxuICAgICAgaWNvbjogXCLwn5OdXCIsXG4gICAgICBjb250ZW50OiBcIiMgTWVldGluZyBOb3Rlc1xcblxcbioqRGF0ZToqKiBbRGF0ZV1cXG4qKkF0dGVuZGVlczoqKiBbTmFtZXNdXFxuKipBZ2VuZGE6KipcXG5cXG4jIyBEaXNjdXNzaW9uIFBvaW50c1xcblxcbiMjIEFjdGlvbiBJdGVtc1xcblxcbiMjIE5leHQgU3RlcHNcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlN0cnVjdHVyZWQgbWVldGluZyBkb2N1bWVudGF0aW9uXCIsXG4gICAgICBjYXRlZ29yeTogJ2J1c2luZXNzJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICc1JyxcbiAgICAgIG5hbWU6IFwiUHJvamVjdCBQbGFuXCIsXG4gICAgICBpY29uOiBcIvCfjq9cIixcbiAgICAgIGNvbnRlbnQ6IFwiIyBQcm9qZWN0IFBsYW5cXG5cXG4jIyBPYmplY3RpdmVcXG5cXG4jIyBTY29wZVxcblxcbiMjIFRpbWVsaW5lXFxuXFxuIyMgUmVzb3VyY2VzXFxuXFxuIyMgTWlsZXN0b25lc1xcblxcbiMjIFJpc2sgQXNzZXNzbWVudFwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiQ29tcHJlaGVuc2l2ZSBwcm9qZWN0IHBsYW5uaW5nXCIsXG4gICAgICBjYXRlZ29yeTogJ2J1c2luZXNzJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICc2JyxcbiAgICAgIG5hbWU6IFwiQmxvZyBQb3N0XCIsXG4gICAgICBpY29uOiBcIvCfk7BcIixcbiAgICAgIGNvbnRlbnQ6IFwiIyBCbG9nIFBvc3QgVGl0bGVcXG5cXG4qUHVibGlzaGVkOiBbRGF0ZV0qXFxuKlRhZ3M6IFt0YWcxLCB0YWcyXSpcXG5cXG4jIyBJbnRyb2R1Y3Rpb25cXG5cXG4jIyBNYWluIENvbnRlbnRcXG5cXG4jIyBDb25jbHVzaW9uXFxuXFxuLS0tXFxuKldoYXQgZG8geW91IHRoaW5rPyBMZWF2ZSBhIGNvbW1lbnQgYmVsb3chKlwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiQmxvZyBwb3N0IHN0cnVjdHVyZVwiLFxuICAgICAgY2F0ZWdvcnk6ICd3cml0aW5nJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICc3JyxcbiAgICAgIG5hbWU6IFwiQVBJIERvY3VtZW50YXRpb25cIixcbiAgICAgIGljb246IFwi8J+UjFwiLFxuICAgICAgY29udGVudDogXCIjIEFQSSBEb2N1bWVudGF0aW9uXFxuXFxuIyMgRW5kcG9pbnRzXFxuXFxuIyMjIEdFVCAvYXBpL2VuZHBvaW50XFxuXFxuKipEZXNjcmlwdGlvbjoqKiBcXG5cXG4qKlBhcmFtZXRlcnM6KipcXG5cXG4qKlJlc3BvbnNlOioqXFxuXFxuYGBganNvblxcbntcXG4gIFxcXCJzdGF0dXNcXFwiOiBcXFwic3VjY2Vzc1xcXCJcXG59XFxuYGBgXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJBUEkgcmVmZXJlbmNlIHRlbXBsYXRlXCIsXG4gICAgICBjYXRlZ29yeTogJ3RlY2huaWNhbCdcbiAgICB9XG4gIF07XG5cbiAgY29uc3QgY2F0ZWdvcmllcyA9IFtcbiAgICB7IGlkOiAnYWxsJywgbmFtZTogJ0FsbCcsIGljb246ICfwn5OaJyB9LFxuICAgIHsgaWQ6ICd3cml0aW5nJywgbmFtZTogJ1dyaXRpbmcnLCBpY29uOiAn4pyN77iPJyB9LFxuICAgIHsgaWQ6ICdidXNpbmVzcycsIG5hbWU6ICdCdXNpbmVzcycsIGljb246ICfwn5K8JyB9LFxuICAgIHsgaWQ6ICd0ZWNobmljYWwnLCBuYW1lOiAnVGVjaG5pY2FsJywgaWNvbjogJ+Kame+4jycgfSxcbiAgICB7IGlkOiAnY3JlYXRpdmUnLCBuYW1lOiAnQ3JlYXRpdmUnLCBpY29uOiAn8J+OqCcgfVxuICBdO1xuXG4gIGNvbnN0IGZpbHRlcmVkVGVtcGxhdGVzID0gc2VsZWN0ZWRDYXRlZ29yeSA9PT0gJ2FsbCdcbiAgICA/IHRlbXBsYXRlc1xuICAgIDogdGVtcGxhdGVzLmZpbHRlcih0ID0+IHQuY2F0ZWdvcnkgPT09IHNlbGVjdGVkQ2F0ZWdvcnkpO1xuXG4gIGNvbnN0IGxvYWRUZW1wbGF0ZSA9ICh0ZW1wbGF0ZTogVGVtcGxhdGUpID0+IHtcbiAgICAvLyBTdG9yZSB0aGUgdGVtcGxhdGUgY29udGVudCB0byBiZSBsb2FkZWQgYnkgdGhlIGVkaXRvclxuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdzY3JvbGwtY29udGVudCcsIHRlbXBsYXRlLmNvbnRlbnQpO1xuICAgIC8vIFRyaWdnZXIgYSBjdXN0b20gZXZlbnQgdG8gbm90aWZ5IHRoZSBlZGl0b3JcbiAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ2xvYWRUZW1wbGF0ZScsIHsgZGV0YWlsOiB0ZW1wbGF0ZSB9KSk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImgtZnVsbCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZmxhbWUgdGV4dC1sZyBmb250LWJvbGQgbWItNFwiPvCfk5wgVGVtcGxhdGVzPC9kaXY+XG5cbiAgICAgIHsvKiBDYXRlZ29yeSBmaWx0ZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtemluYy00MDAgbWItMlwiPkNhdGVnb3JpZXM8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMVwiPlxuICAgICAgICAgIHtjYXRlZ29yaWVzLm1hcCgoY2F0ZWdvcnkpID0+IChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAga2V5PXtjYXRlZ29yeS5pZH1cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRDYXRlZ29yeShjYXRlZ29yeS5pZCl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICBzZWxlY3RlZENhdGVnb3J5ID09PSBjYXRlZ29yeS5pZFxuICAgICAgICAgICAgICAgICAgPyAnYmctZ2hvc3RibHVlIHRleHQtemluYy05MDAnXG4gICAgICAgICAgICAgICAgICA6ICdiZy16aW5jLTgwMCB0ZXh0LXppbmMtMzAwIGhvdmVyOmJnLXppbmMtNzAwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2NhdGVnb3J5Lmljb259IHtjYXRlZ29yeS5uYW1lfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBUZW1wbGF0ZXMgbGlzdCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHNwYWNlLXktMiBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAge2ZpbHRlcmVkVGVtcGxhdGVzLm1hcCgodGVtcGxhdGUpID0+IChcbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBrZXk9e3RlbXBsYXRlLmlkfVxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gbG9hZFRlbXBsYXRlKHRlbXBsYXRlKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWxlZnQgcC0zIHJvdW5kZWQtbGcgYmctemluYy04MDAgaG92ZXI6YmctemluYy03MDAgYm9yZGVyIGJvcmRlci16aW5jLTcwMCBob3Zlcjpib3JkZXItemluYy02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZ3JvdXBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtM1wiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZsZXgtc2hyaW5rLTBcIj57dGVtcGxhdGUuaWNvbn08L3NwYW4+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbSBmb250LW1lZGl1bSBncm91cC1ob3Zlcjp0ZXh0LWdob3N0Ymx1ZSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAge3RlbXBsYXRlLm5hbWV9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXppbmMtNDAwIHRleHQteHMgbXQtMSBsaW5lLWNsYW1wLTJcIj5cbiAgICAgICAgICAgICAgICAgIHt0ZW1wbGF0ZS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEFjdGlvbnMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgcHQtNCBib3JkZXItdCBib3JkZXItemluYy03MDAgc3BhY2UteS0yXCI+XG4gICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdob3N0Ymx1ZSBob3ZlcjpiZy1naG9zdGJsdWUvODAgdGV4dC16aW5jLTkwMCBweS0yIHB4LTQgcm91bmRlZCBmb250LW1lZGl1bSB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgKyBOZXcgVGVtcGxhdGVcbiAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidy1mdWxsIGJnLXppbmMtODAwIGhvdmVyOmJnLXppbmMtNzAwIHRleHQtd2hpdGUgcHktMiBweC00IHJvdW5kZWQgZm9udC1tZWRpdW0gdGV4dC1zbSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgIPCfk4EgQnJvd3NlIFNjcm9sbHNcbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIlRlbXBsYXRlU2lkZWJhciIsInNlbGVjdGVkQ2F0ZWdvcnkiLCJzZXRTZWxlY3RlZENhdGVnb3J5IiwidGVtcGxhdGVzIiwiaWQiLCJuYW1lIiwiaWNvbiIsImNvbnRlbnQiLCJkZXNjcmlwdGlvbiIsImNhdGVnb3J5IiwiY2F0ZWdvcmllcyIsImZpbHRlcmVkVGVtcGxhdGVzIiwiZmlsdGVyIiwidCIsImxvYWRUZW1wbGF0ZSIsInRlbXBsYXRlIiwibG9jYWxTdG9yYWdlIiwic2V0SXRlbSIsIndpbmRvdyIsImRpc3BhdGNoRXZlbnQiLCJDdXN0b21FdmVudCIsImRldGFpbCIsImRpdiIsImNsYXNzTmFtZSIsIm1hcCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TemplateSidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ThemeSelector.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeSelector.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst themes = [\n    {\n        id: 'flame-empire',\n        name: 'Flame Empire',\n        description: 'The original sovereign theme',\n        colors: {\n            primary: '#FF6B00',\n            secondary: '#2DD4BF',\n            background: '#0D0D1A',\n            surface: '#1E1B24',\n            text: '#FFFFFF',\n            accent: '#FF6B00'\n        },\n        icon: '🔥'\n    },\n    {\n        id: 'midnight-scholar',\n        name: 'Midnight Scholar',\n        description: 'Deep blues for focused writing',\n        colors: {\n            primary: '#3B82F6',\n            secondary: '#8B5CF6',\n            background: '#0F172A',\n            surface: '#1E293B',\n            text: '#F8FAFC',\n            accent: '#60A5FA'\n        },\n        icon: '🌙'\n    },\n    {\n        id: 'forest-sage',\n        name: 'Forest Sage',\n        description: 'Natural greens for calm creativity',\n        colors: {\n            primary: '#10B981',\n            secondary: '#34D399',\n            background: '#064E3B',\n            surface: '#065F46',\n            text: '#ECFDF5',\n            accent: '#6EE7B7'\n        },\n        icon: '🌲'\n    },\n    {\n        id: 'royal-purple',\n        name: 'Royal Purple',\n        description: 'Majestic purples for elegant writing',\n        colors: {\n            primary: '#8B5CF6',\n            secondary: '#A78BFA',\n            background: '#1E1B4B',\n            surface: '#312E81',\n            text: '#F3F4F6',\n            accent: '#C4B5FD'\n        },\n        icon: '👑'\n    },\n    {\n        id: 'sunset-writer',\n        name: 'Sunset Writer',\n        description: 'Warm oranges and pinks',\n        colors: {\n            primary: '#F59E0B',\n            secondary: '#EC4899',\n            background: '#451A03',\n            surface: '#92400E',\n            text: '#FEF3C7',\n            accent: '#FBBF24'\n        },\n        icon: '🌅'\n    }\n];\nfunction ThemeSelector() {\n    _s();\n    const [currentTheme, setCurrentTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('flame-empire');\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeSelector.useEffect\": ()=>{\n            // Load saved theme\n            const savedTheme = localStorage.getItem('writeos-theme');\n            if (savedTheme) {\n                setCurrentTheme(savedTheme);\n                applyTheme(savedTheme);\n            }\n        }\n    }[\"ThemeSelector.useEffect\"], []);\n    const applyTheme = (themeId)=>{\n        const theme = themes.find((t)=>t.id === themeId);\n        if (!theme) return;\n        const root = document.documentElement;\n        // Apply CSS custom properties\n        root.style.setProperty('--color-primary', theme.colors.primary);\n        root.style.setProperty('--color-secondary', theme.colors.secondary);\n        root.style.setProperty('--color-background', theme.colors.background);\n        root.style.setProperty('--color-surface', theme.colors.surface);\n        root.style.setProperty('--color-text', theme.colors.text);\n        root.style.setProperty('--color-accent', theme.colors.accent);\n        // Update Tailwind classes dynamically\n        const style = document.createElement('style');\n        style.innerHTML = \"\\n      :root {\\n        --flame: \".concat(theme.colors.primary, \";\\n        --ghostblue: \").concat(theme.colors.secondary, \";\\n        --scrollbg: \").concat(theme.colors.background, \";\\n        --shadowline: \").concat(theme.colors.surface, \";\\n      }\\n      \\n      .bg-flame { background-color: \").concat(theme.colors.primary, \" !important; }\\n      .text-flame { color: \").concat(theme.colors.primary, \" !important; }\\n      .bg-ghostblue { background-color: \").concat(theme.colors.secondary, \" !important; }\\n      .text-ghostblue { color: \").concat(theme.colors.secondary, \" !important; }\\n      .bg-scrollbg { background-color: \").concat(theme.colors.background, \" !important; }\\n      .border-shadowline { border-color: \").concat(theme.colors.surface, \" !important; }\\n    \");\n        // Remove old theme styles\n        const oldStyle = document.getElementById('theme-styles');\n        if (oldStyle) {\n            oldStyle.remove();\n        }\n        style.id = 'theme-styles';\n        document.head.appendChild(style);\n    };\n    const selectTheme = (themeId)=>{\n        setCurrentTheme(themeId);\n        applyTheme(themeId);\n        localStorage.setItem('writeos-theme', themeId);\n        setIsOpen(false);\n    };\n    const currentThemeData = themes.find((t)=>t.id === currentTheme);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 left-4 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"bg-zinc-800 hover:bg-zinc-700 border border-zinc-600 text-white p-2 rounded-lg shadow-lg transition-all duration-300 flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg\",\n                        children: currentThemeData === null || currentThemeData === void 0 ? void 0 : currentThemeData.icon\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium hidden sm:block\",\n                        children: currentThemeData === null || currentThemeData === void 0 ? void 0 : currentThemeData.name\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-12 left-0 bg-zinc-900 border border-zinc-700 rounded-lg p-4 w-80 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-flame font-bold text-lg\",\n                                children: \"\\uD83C\\uDFA8 Theme Selector\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(false),\n                                className: \"text-zinc-400 hover:text-white\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: themes.map((theme)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>selectTheme(theme.id),\n                                className: \"w-full text-left p-3 rounded-lg border transition-all duration-200 \".concat(currentTheme === theme.id ? 'border-flame bg-zinc-800' : 'border-zinc-700 bg-zinc-800 hover:bg-zinc-700 hover:border-zinc-600'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: theme.icon\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-white mb-1\",\n                                                    children: theme.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-zinc-400 mb-2\",\n                                                    children: theme.description\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 rounded-full border border-zinc-600\",\n                                                            style: {\n                                                                backgroundColor: theme.colors.primary\n                                                            },\n                                                            title: \"Primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 rounded-full border border-zinc-600\",\n                                                            style: {\n                                                                backgroundColor: theme.colors.secondary\n                                                            },\n                                                            title: \"Secondary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 rounded-full border border-zinc-600\",\n                                                            style: {\n                                                                backgroundColor: theme.colors.background\n                                                            },\n                                                            title: \"Background\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 rounded-full border border-zinc-600\",\n                                                            style: {\n                                                                backgroundColor: theme.colors.surface\n                                                            },\n                                                            title: \"Surface\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 19\n                                        }, this),\n                                        currentTheme === theme.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-flame text-sm\",\n                                            children: \"✓\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, this)\n                            }, theme.id, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 pt-4 border-t border-zinc-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-zinc-400\",\n                            children: \"\\uD83D\\uDCA1 Themes are automatically saved and will persist across sessions\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ThemeSelector.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeSelector, \"2NdLe21e0+Sm7VaKjXhxDgYbs88=\");\n_c = ThemeSelector;\nvar _c;\n$RefreshReg$(_c, \"ThemeSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ThemeSelector.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FThemeSelector.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);