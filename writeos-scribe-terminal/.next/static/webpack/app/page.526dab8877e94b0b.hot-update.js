"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ScrollEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/ScrollEditor.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ScrollEditor() {\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isPreview, setIsPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            if (content.trim()) {\n                const timer = setTimeout({\n                    \"ScrollEditor.useEffect.timer\": ()=>{\n                        // Simulate auto-save\n                        localStorage.setItem('scroll-content', content);\n                        setLastSaved(new Date());\n                    }\n                }[\"ScrollEditor.useEffect.timer\"], 2000);\n                return ({\n                    \"ScrollEditor.useEffect\": ()=>clearTimeout(timer)\n                })[\"ScrollEditor.useEffect\"];\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], [\n        content\n    ]);\n    // Load saved content on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const saved = localStorage.getItem('scroll-content');\n            if (saved) {\n                setContent(saved);\n                setLastSaved(new Date());\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Listen for template loading events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const handleLoadTemplate = {\n                \"ScrollEditor.useEffect.handleLoadTemplate\": (event)=>{\n                    const template = event.detail;\n                    setContent(template.content);\n                    setLastSaved(null); // Reset save status for new template\n                    // Focus the editor\n                    setTimeout({\n                        \"ScrollEditor.useEffect.handleLoadTemplate\": ()=>{\n                            var _textareaRef_current;\n                            (_textareaRef_current = textareaRef.current) === null || _textareaRef_current === void 0 ? void 0 : _textareaRef_current.focus();\n                        }\n                    }[\"ScrollEditor.useEffect.handleLoadTemplate\"], 100);\n                }\n            }[\"ScrollEditor.useEffect.handleLoadTemplate\"];\n            window.addEventListener('loadTemplate', handleLoadTemplate);\n            return ({\n                \"ScrollEditor.useEffect\": ()=>{\n                    window.removeEventListener('loadTemplate', handleLoadTemplate);\n                }\n            })[\"ScrollEditor.useEffect\"];\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Calculate stats\n    const wordCount = content.trim() ? content.trim().split(/\\s+/).length : 0;\n    const charCount = content.length;\n    const lineCount = content.split('\\n').length;\n    // Simple markdown preview (basic implementation)\n    const renderMarkdown = (text)=>{\n        return text.replace(/^# (.*$)/gim, '<h1 class=\"text-2xl font-bold text-ghostblue mb-4\">$1</h1>').replace(/^## (.*$)/gim, '<h2 class=\"text-xl font-bold text-white mb-3\">$1</h2>').replace(/^### (.*$)/gim, '<h3 class=\"text-lg font-bold text-zinc-300 mb-2\">$1</h3>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong class=\"font-bold text-white\">$1</strong>').replace(/\\*(.*?)\\*/g, '<em class=\"italic text-zinc-300\">$1</em>').replace(/`(.*?)`/g, '<code class=\"bg-zinc-800 text-flame px-1 rounded\">$1</code>').replace(/\\n/g, '<br>');\n    };\n    const handleKeyDown = (e)=>{\n        // Tab support\n        if (e.key === 'Tab') {\n            e.preventDefault();\n            const start = e.currentTarget.selectionStart;\n            const end = e.currentTarget.selectionEnd;\n            const newContent = content.substring(0, start) + '  ' + content.substring(end);\n            setContent(newContent);\n            // Restore cursor position\n            setTimeout(()=>{\n                if (textareaRef.current) {\n                    textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 2;\n                }\n            }, 0);\n        }\n    };\n    const insertTemplate = (template)=>{\n        const templates = {\n            heading: '# Your Heading Here\\n\\n',\n            list: '- Item 1\\n- Item 2\\n- Item 3\\n\\n',\n            code: '```\\nYour code here\\n```\\n\\n',\n            quote: '> Your quote here\\n\\n',\n            table: '| Column 1 | Column 2 |\\n|----------|----------|\\n| Cell 1   | Cell 2   |\\n\\n'\n        };\n        const insertion = templates[template] || '';\n        const textarea = textareaRef.current;\n        if (textarea) {\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const newContent = content.substring(0, start) + insertion + content.substring(end);\n            setContent(newContent);\n            // Focus and position cursor\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.selectionStart = textarea.selectionEnd = start + insertion.length;\n            }, 0);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-ghostblue text-lg font-bold\",\n                        children: \"✍️ Scroll Editor\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsPreview(!isPreview),\n                                className: \"px-3 py-1 rounded text-xs font-medium transition-colors \".concat(isPreview ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-700 text-white hover:bg-zinc-600'),\n                                children: isPreview ? '📝 Edit' : '👁️ Preview'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    '🔥 Enhance',\n                                    '📋 Summarize',\n                                    '🎨 Format',\n                                    '📊 Analyze'\n                                ].map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleAscend(action.split(' ')[1].toLowerCase()),\n                                        className: \"px-2 py-1 bg-flame hover:bg-flame/80 text-white text-xs rounded font-medium transition-colors\",\n                                        disabled: !content.trim(),\n                                        children: action\n                                    }, action, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-zinc-500\",\n                                children: lastSaved ? \"Saved \".concat(lastSaved.toLocaleTimeString()) : 'Not saved'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-1 mb-3\",\n                children: [\n                    {\n                        label: 'H1',\n                        action: ()=>insertTemplate('heading')\n                    },\n                    {\n                        label: 'List',\n                        action: ()=>insertTemplate('list')\n                    },\n                    {\n                        label: 'Code',\n                        action: ()=>insertTemplate('code')\n                    },\n                    {\n                        label: 'Quote',\n                        action: ()=>insertTemplate('quote')\n                    },\n                    {\n                        label: 'Table',\n                        action: ()=>insertTemplate('table')\n                    }\n                ].map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: tool.action,\n                        className: \"px-2 py-1 bg-zinc-800 hover:bg-zinc-700 text-zinc-300 text-xs rounded transition-colors\",\n                        children: tool.label\n                    }, tool.label, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg overflow-hidden\",\n                children: isPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-4 overflow-y-auto prose prose-invert max-w-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white leading-relaxed\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(content)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: content,\n                    onChange: (e)=>setContent(e.target.value),\n                    onKeyDown: handleKeyDown,\n                    className: \"w-full h-full bg-transparent text-white resize-none outline-none p-4 font-mono text-sm leading-relaxed\",\n                    placeholder: \"Begin your scroll here...  # Welcome to your Scroll Editor  Start writing in **Markdown** format: - Use # for headings - Use **bold** and *italic* text - Create `code snippets` - Make lists and more!  Press Tab for indentation, Ctrl+Enter for preview.\",\n                    spellCheck: false\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 flex items-center justify-between text-xs text-zinc-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Words: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: wordCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Characters: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: charCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Lines: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: lineCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            content.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-ghostblue rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Ready\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_s(ScrollEditor, \"RY4gjijf6zMgAVQrPlad4F9++uo=\");\n_c = ScrollEditor;\nvar _c;\n$RefreshReg$(_c, \"ScrollEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ScrollEditor.tsx\n"));

/***/ })

});