"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ScribeChatPanel.tsx":
/*!********************************************!*\
  !*** ./src/components/ScribeChatPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScribeChatPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ScribeChatPanel() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            role: 'assistant',\n            content: 'Welcome to your AI writing assistant. How can I help you craft your scroll today?',\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScribeChatPanel.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ScribeChatPanel.useEffect\"], [\n        messages\n    ]);\n    const sendMessage = async ()=>{\n        if (!input.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: input.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput('');\n        setIsLoading(true);\n        try {\n            // Phase III - Real AI Integration\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    history: messages.slice(-5),\n                    context: 'writing-assistant'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                const aiResponse = {\n                    id: (Date.now() + 1).toString(),\n                    role: 'assistant',\n                    content: result.message,\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiResponse\n                    ]);\n                // Update analytics\n                updateAnalytics('chatMessage');\n            } else {\n                // Fallback to local response\n                const aiResponse = {\n                    id: (Date.now() + 1).toString(),\n                    role: 'assistant',\n                    content: generateAIResponse(userMessage.content),\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiResponse\n                    ]);\n            }\n        } catch (error) {\n            console.error('Error sending message:', error);\n            // Fallback response\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                role: 'assistant',\n                content: \"I apologize, but I'm having trouble connecting right now. Please try again in a moment.\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiResponse\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateAnalytics = (action)=>{\n        try {\n            const stored = localStorage.getItem('writeos-analytics');\n            if (stored) {\n                const analytics = JSON.parse(stored);\n                if (action === 'chatMessage') {\n                    analytics.aiUsageStats.chatMessages += 1;\n                }\n                localStorage.setItem('writeos-analytics', JSON.stringify(analytics));\n            }\n        } catch (error) {\n            console.error('Error updating analytics:', error);\n        }\n    };\n    const generateAIResponse = (userInput)=>{\n        // Enhanced AI response logic\n        const responses = {\n            writing: [\n                \"I can help you structure your thoughts. What type of document are you working on?\",\n                \"Let's break this down into clear sections. What's your main objective?\",\n                \"Consider starting with an outline. What are the key points you want to cover?\"\n            ],\n            editing: [\n                \"I can help refine your prose. Share a paragraph and I'll suggest improvements.\",\n                \"Let's focus on clarity and flow. What section needs the most work?\",\n                \"Consider your audience - who are you writing for?\"\n            ],\n            ideas: [\n                \"Let's brainstorm! What's the core concept you want to explore?\",\n                \"I can help generate creative angles. What's your starting point?\",\n                \"What if we approached this from a different perspective?\"\n            ],\n            default: [\n                \"I'm here to help with your writing. What would you like to work on?\",\n                \"Let's craft something amazing together. What's on your mind?\",\n                \"I can assist with writing, editing, brainstorming, or structuring. How can I help?\"\n            ]\n        };\n        const input = userInput.toLowerCase();\n        if (input.includes('write') || input.includes('draft')) {\n            return responses.writing[Math.floor(Math.random() * responses.writing.length)];\n        } else if (input.includes('edit') || input.includes('improve') || input.includes('fix')) {\n            return responses.editing[Math.floor(Math.random() * responses.editing.length)];\n        } else if (input.includes('idea') || input.includes('brainstorm') || input.includes('think')) {\n            return responses.ideas[Math.floor(Math.random() * responses.ideas.length)];\n        } else {\n            return responses.default[Math.floor(Math.random() * responses.default.length)];\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-flame text-lg font-bold mb-4\",\n                children: \"\\uD83E\\uDD16 Scribe Assistant\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg p-4 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto mb-4 space-y-3\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-[80%] p-3 rounded-lg text-sm \".concat(message.role === 'user' ? 'bg-flame text-white' : 'bg-zinc-700 text-zinc-100'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-60 mt-1\",\n                                                children: message.timestamp.toLocaleTimeString([], {\n                                                    hour: '2-digit',\n                                                    minute: '2-digit'\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                }, message.id, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-zinc-700 text-zinc-100 p-3 rounded-lg text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Thinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                className: \"flex-1 bg-zinc-800 border border-zinc-700 rounded px-3 py-2 text-white text-sm focus:outline-none focus:border-ghostblue\",\n                                placeholder: \"Ask your scribe assistant...\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: sendMessage,\n                                disabled: !input.trim() || isLoading,\n                                className: \"bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded text-sm font-medium transition-colors\",\n                                children: \"Send\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n_s(ScribeChatPanel, \"a/3GhHbUQ1u1bp48TjjmheS6EE4=\");\n_c = ScribeChatPanel;\nvar _c;\n$RefreshReg$(_c, \"ScribeChatPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ScribeChatPanel.tsx\n"));

/***/ })

});