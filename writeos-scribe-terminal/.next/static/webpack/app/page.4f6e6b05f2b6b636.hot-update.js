"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ScrollEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/ScrollEditor.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ScrollEditor() {\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isPreview, setIsPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            if (content.trim()) {\n                const timer = setTimeout({\n                    \"ScrollEditor.useEffect.timer\": ()=>{\n                        // Simulate auto-save\n                        localStorage.setItem('scroll-content', content);\n                        setLastSaved(new Date());\n                    }\n                }[\"ScrollEditor.useEffect.timer\"], 2000);\n                return ({\n                    \"ScrollEditor.useEffect\": ()=>clearTimeout(timer)\n                })[\"ScrollEditor.useEffect\"];\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], [\n        content\n    ]);\n    // Load saved content on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const saved = localStorage.getItem('scroll-content');\n            if (saved) {\n                setContent(saved);\n                setLastSaved(new Date());\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Calculate stats\n    const wordCount = content.trim() ? content.trim().split(/\\s+/).length : 0;\n    const charCount = content.length;\n    const lineCount = content.split('\\n').length;\n    // Simple markdown preview (basic implementation)\n    const renderMarkdown = (text)=>{\n        return text.replace(/^# (.*$)/gim, '<h1 class=\"text-2xl font-bold text-ghostblue mb-4\">$1</h1>').replace(/^## (.*$)/gim, '<h2 class=\"text-xl font-bold text-white mb-3\">$1</h2>').replace(/^### (.*$)/gim, '<h3 class=\"text-lg font-bold text-zinc-300 mb-2\">$1</h3>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong class=\"font-bold text-white\">$1</strong>').replace(/\\*(.*?)\\*/g, '<em class=\"italic text-zinc-300\">$1</em>').replace(/`(.*?)`/g, '<code class=\"bg-zinc-800 text-flame px-1 rounded\">$1</code>').replace(/\\n/g, '<br>');\n    };\n    const handleKeyDown = (e)=>{\n        // Tab support\n        if (e.key === 'Tab') {\n            e.preventDefault();\n            const start = e.currentTarget.selectionStart;\n            const end = e.currentTarget.selectionEnd;\n            const newContent = content.substring(0, start) + '  ' + content.substring(end);\n            setContent(newContent);\n            // Restore cursor position\n            setTimeout(()=>{\n                if (textareaRef.current) {\n                    textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 2;\n                }\n            }, 0);\n        }\n    };\n    const insertTemplate = (template)=>{\n        const templates = {\n            heading: '# Your Heading Here\\n\\n',\n            list: '- Item 1\\n- Item 2\\n- Item 3\\n\\n',\n            code: '```\\nYour code here\\n```\\n\\n',\n            quote: '> Your quote here\\n\\n',\n            table: '| Column 1 | Column 2 |\\n|----------|----------|\\n| Cell 1   | Cell 2   |\\n\\n'\n        };\n        const insertion = templates[template] || '';\n        const textarea = textareaRef.current;\n        if (textarea) {\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const newContent = content.substring(0, start) + insertion + content.substring(end);\n            setContent(newContent);\n            // Focus and position cursor\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.selectionStart = textarea.selectionEnd = start + insertion.length;\n            }, 0);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-ghostblue text-lg font-bold\",\n                        children: \"✍️ Scroll Editor\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsPreview(!isPreview),\n                                className: \"px-3 py-1 rounded text-xs font-medium transition-colors \".concat(isPreview ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-700 text-white hover:bg-zinc-600'),\n                                children: isPreview ? '📝 Edit' : '👁️ Preview'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-zinc-500\",\n                                children: lastSaved ? \"Saved \".concat(lastSaved.toLocaleTimeString()) : 'Not saved'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-1 mb-3\",\n                children: [\n                    {\n                        label: 'H1',\n                        action: ()=>insertTemplate('heading')\n                    },\n                    {\n                        label: 'List',\n                        action: ()=>insertTemplate('list')\n                    },\n                    {\n                        label: 'Code',\n                        action: ()=>insertTemplate('code')\n                    },\n                    {\n                        label: 'Quote',\n                        action: ()=>insertTemplate('quote')\n                    },\n                    {\n                        label: 'Table',\n                        action: ()=>insertTemplate('table')\n                    }\n                ].map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: tool.action,\n                        className: \"px-2 py-1 bg-zinc-800 hover:bg-zinc-700 text-zinc-300 text-xs rounded transition-colors\",\n                        children: tool.label\n                    }, tool.label, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg overflow-hidden\",\n                children: isPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-4 overflow-y-auto prose prose-invert max-w-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white leading-relaxed\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(content)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: content,\n                    onChange: (e)=>setContent(e.target.value),\n                    onKeyDown: handleKeyDown,\n                    className: \"w-full h-full bg-transparent text-white resize-none outline-none p-4 font-mono text-sm leading-relaxed\",\n                    placeholder: \"Begin your scroll here...  # Welcome to your Scroll Editor  Start writing in **Markdown** format: - Use # for headings - Use **bold** and *italic* text - Create `code snippets` - Make lists and more!  Press Tab for indentation, Ctrl+Enter for preview.\",\n                    spellCheck: false\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 flex items-center justify-between text-xs text-zinc-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Words: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: wordCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Characters: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: charCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Lines: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: lineCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            content.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-ghostblue rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Ready\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(ScrollEditor, \"byif1FxyauhiSopOgqpF6GW5GJU=\");\n_c = ScrollEditor;\nvar _c;\n$RefreshReg$(_c, \"ScrollEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ScrollEditor.tsx\n"));

/***/ })

});