"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ScrollEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/ScrollEditor.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ScrollEditor() {\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isPreview, setIsPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            if (content.trim()) {\n                const timer = setTimeout({\n                    \"ScrollEditor.useEffect.timer\": ()=>{\n                        // Simulate auto-save\n                        localStorage.setItem('scroll-content', content);\n                        setLastSaved(new Date());\n                    }\n                }[\"ScrollEditor.useEffect.timer\"], 2000);\n                return ({\n                    \"ScrollEditor.useEffect\": ()=>clearTimeout(timer)\n                })[\"ScrollEditor.useEffect\"];\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], [\n        content\n    ]);\n    // Load saved content on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const saved = localStorage.getItem('scroll-content');\n            if (saved) {\n                setContent(saved);\n                setLastSaved(new Date());\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Listen for template loading events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const handleLoadTemplate = {\n                \"ScrollEditor.useEffect.handleLoadTemplate\": (event)=>{\n                    const template = event.detail;\n                    setContent(template.content);\n                    setLastSaved(null); // Reset save status for new template\n                    // Focus the editor\n                    setTimeout({\n                        \"ScrollEditor.useEffect.handleLoadTemplate\": ()=>{\n                            var _textareaRef_current;\n                            (_textareaRef_current = textareaRef.current) === null || _textareaRef_current === void 0 ? void 0 : _textareaRef_current.focus();\n                        }\n                    }[\"ScrollEditor.useEffect.handleLoadTemplate\"], 100);\n                }\n            }[\"ScrollEditor.useEffect.handleLoadTemplate\"];\n            window.addEventListener('loadTemplate', handleLoadTemplate);\n            return ({\n                \"ScrollEditor.useEffect\": ()=>{\n                    window.removeEventListener('loadTemplate', handleLoadTemplate);\n                }\n            })[\"ScrollEditor.useEffect\"];\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Calculate stats\n    const wordCount = content.trim() ? content.trim().split(/\\s+/).length : 0;\n    const charCount = content.length;\n    const lineCount = content.split('\\n').length;\n    // Simple markdown preview (basic implementation)\n    const renderMarkdown = (text)=>{\n        return text.replace(/^# (.*$)/gim, '<h1 class=\"text-2xl font-bold text-ghostblue mb-4\">$1</h1>').replace(/^## (.*$)/gim, '<h2 class=\"text-xl font-bold text-white mb-3\">$1</h2>').replace(/^### (.*$)/gim, '<h3 class=\"text-lg font-bold text-zinc-300 mb-2\">$1</h3>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong class=\"font-bold text-white\">$1</strong>').replace(/\\*(.*?)\\*/g, '<em class=\"italic text-zinc-300\">$1</em>').replace(/`(.*?)`/g, '<code class=\"bg-zinc-800 text-flame px-1 rounded\">$1</code>').replace(/\\n/g, '<br>');\n    };\n    const handleKeyDown = (e)=>{\n        // Tab support\n        if (e.key === 'Tab') {\n            e.preventDefault();\n            const start = e.currentTarget.selectionStart;\n            const end = e.currentTarget.selectionEnd;\n            const newContent = content.substring(0, start) + '  ' + content.substring(end);\n            setContent(newContent);\n            // Restore cursor position\n            setTimeout(()=>{\n                if (textareaRef.current) {\n                    textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 2;\n                }\n            }, 0);\n        }\n    };\n    const insertTemplate = (template)=>{\n        const templates = {\n            heading: '# Your Heading Here\\n\\n',\n            list: '- Item 1\\n- Item 2\\n- Item 3\\n\\n',\n            code: '```\\nYour code here\\n```\\n\\n',\n            quote: '> Your quote here\\n\\n',\n            table: '| Column 1 | Column 2 |\\n|----------|----------|\\n| Cell 1   | Cell 2   |\\n\\n'\n        };\n        const insertion = templates[template] || '';\n        const textarea = textareaRef.current;\n        if (textarea) {\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const newContent = content.substring(0, start) + insertion + content.substring(end);\n            setContent(newContent);\n            // Focus and position cursor\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.selectionStart = textarea.selectionEnd = start + insertion.length;\n            }, 0);\n        }\n    };\n    const handleAscend = async (action)=>{\n        if (!content.trim() || isProcessing) return;\n        setIsProcessing(true);\n        try {\n            const response = await fetch('/api/ascend', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content,\n                    action,\n                    context: 'scroll-editor'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                var _result_metadata;\n                setContent(result.processedContent);\n                setLastSaved(null); // Reset save status\n                // Update analytics\n                updateAnalytics(action);\n                // Show success notification with AI provider info\n                console.log('Ascend completed:', result.message);\n                console.log('AI Provider:', result.aiProvider || 'Fallback System');\n                console.log('Processing Time:', ((_result_metadata = result.metadata) === null || _result_metadata === void 0 ? void 0 : _result_metadata.processingTime) || 'N/A');\n                console.log('Suggestions:', result.suggestions);\n                // You could add a toast notification here\n                showNotification(\"\".concat(action, \" completed successfully!\"), 'success');\n            } else {\n                console.error('Ascend failed:', result.error);\n                showNotification(\"\".concat(action, \" failed. Please try again.\"), 'error');\n            }\n        } catch (error) {\n            console.error('Ascend error:', error);\n            showNotification('Connection error. Please check your internet.', 'error');\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    const updateAnalytics = (action)=>{\n        try {\n            const stored = localStorage.getItem('writeos-analytics');\n            if (stored) {\n                const analytics = JSON.parse(stored);\n                switch(action){\n                    case 'enhance':\n                        analytics.aiUsageStats.enhanceCount += 1;\n                        break;\n                    case 'summarize':\n                        analytics.aiUsageStats.summarizeCount += 1;\n                        break;\n                    case 'analyze':\n                        analytics.aiUsageStats.analyzeCount += 1;\n                        break;\n                }\n                localStorage.setItem('writeos-analytics', JSON.stringify(analytics));\n            }\n        } catch (error) {\n            console.error('Error updating analytics:', error);\n        }\n    };\n    const showNotification = (message, type)=>{\n        // Simple notification system - you could enhance this with a proper toast library\n        console.log(\"[\".concat(type.toUpperCase(), \"] \").concat(message));\n        // Create a temporary visual notification\n        const notification = document.createElement('div');\n        notification.className = \"fixed top-4 right-4 p-3 rounded-lg text-white z-50 \".concat(type === 'success' ? 'bg-green-600' : 'bg-red-600');\n        notification.textContent = message;\n        document.body.appendChild(notification);\n        setTimeout(()=>{\n            document.body.removeChild(notification);\n        }, 3000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-ghostblue text-lg font-bold\",\n                        children: \"✍️ Scroll Editor\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsPreview(!isPreview),\n                                className: \"px-3 py-1 rounded text-xs font-medium transition-colors \".concat(isPreview ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-700 text-white hover:bg-zinc-600'),\n                                children: isPreview ? '📝 Edit' : '👁️ Preview'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    '🔥 Enhance',\n                                    '📋 Summarize',\n                                    '🎨 Format',\n                                    '📊 Analyze'\n                                ].map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleAscend(action.split(' ')[1].toLowerCase()),\n                                        className: \"px-2 py-1 bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white text-xs rounded font-medium transition-colors\",\n                                        disabled: !content.trim() || isProcessing,\n                                        children: isProcessing ? '⏳' : action\n                                    }, action, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-zinc-500\",\n                                children: lastSaved ? \"Saved \".concat(lastSaved.toLocaleTimeString()) : 'Not saved'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-1 mb-3\",\n                children: [\n                    {\n                        label: 'H1',\n                        action: ()=>insertTemplate('heading')\n                    },\n                    {\n                        label: 'List',\n                        action: ()=>insertTemplate('list')\n                    },\n                    {\n                        label: 'Code',\n                        action: ()=>insertTemplate('code')\n                    },\n                    {\n                        label: 'Quote',\n                        action: ()=>insertTemplate('quote')\n                    },\n                    {\n                        label: 'Table',\n                        action: ()=>insertTemplate('table')\n                    }\n                ].map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: tool.action,\n                        className: \"px-2 py-1 bg-zinc-800 hover:bg-zinc-700 text-zinc-300 text-xs rounded transition-colors\",\n                        children: tool.label\n                    }, tool.label, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg overflow-hidden\",\n                children: isPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-4 overflow-y-auto prose prose-invert max-w-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white leading-relaxed\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(content)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: content,\n                    onChange: (e)=>setContent(e.target.value),\n                    onKeyDown: handleKeyDown,\n                    className: \"w-full h-full bg-transparent text-white resize-none outline-none p-4 font-mono text-sm leading-relaxed\",\n                    placeholder: \"Begin your scroll here...  # Welcome to your Scroll Editor  Start writing in **Markdown** format: - Use # for headings - Use **bold** and *italic* text - Create `code snippets` - Make lists and more!  Press Tab for indentation, Ctrl+Enter for preview.\",\n                    spellCheck: false\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 flex items-center justify-between text-xs text-zinc-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Words: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: wordCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Characters: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: charCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Lines: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: lineCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            content.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-ghostblue rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Ready\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n_s(ScrollEditor, \"RY4gjijf6zMgAVQrPlad4F9++uo=\");\n_c = ScrollEditor;\nvar _c;\n$RefreshReg$(_c, \"ScrollEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ScrollEditor.tsx\n"));

/***/ })

});