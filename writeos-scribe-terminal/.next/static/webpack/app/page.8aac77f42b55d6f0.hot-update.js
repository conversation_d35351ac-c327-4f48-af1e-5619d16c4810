"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ScrollEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/ScrollEditor.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ScrollEditor() {\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isPreview, setIsPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            if (content.trim()) {\n                const timer = setTimeout({\n                    \"ScrollEditor.useEffect.timer\": ()=>{\n                        // Simulate auto-save\n                        localStorage.setItem('scroll-content', content);\n                        setLastSaved(new Date());\n                    }\n                }[\"ScrollEditor.useEffect.timer\"], 2000);\n                return ({\n                    \"ScrollEditor.useEffect\": ()=>clearTimeout(timer)\n                })[\"ScrollEditor.useEffect\"];\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], [\n        content\n    ]);\n    // Load saved content on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const saved = localStorage.getItem('scroll-content');\n            if (saved) {\n                setContent(saved);\n                setLastSaved(new Date());\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Listen for template loading events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const handleLoadTemplate = {\n                \"ScrollEditor.useEffect.handleLoadTemplate\": (event)=>{\n                    const template = event.detail;\n                    setContent(template.content);\n                    setLastSaved(null); // Reset save status for new template\n                    // Focus the editor\n                    setTimeout({\n                        \"ScrollEditor.useEffect.handleLoadTemplate\": ()=>{\n                            var _textareaRef_current;\n                            (_textareaRef_current = textareaRef.current) === null || _textareaRef_current === void 0 ? void 0 : _textareaRef_current.focus();\n                        }\n                    }[\"ScrollEditor.useEffect.handleLoadTemplate\"], 100);\n                }\n            }[\"ScrollEditor.useEffect.handleLoadTemplate\"];\n            window.addEventListener('loadTemplate', handleLoadTemplate);\n            return ({\n                \"ScrollEditor.useEffect\": ()=>{\n                    window.removeEventListener('loadTemplate', handleLoadTemplate);\n                }\n            })[\"ScrollEditor.useEffect\"];\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Calculate stats\n    const wordCount = content.trim() ? content.trim().split(/\\s+/).length : 0;\n    const charCount = content.length;\n    const lineCount = content.split('\\n').length;\n    // Simple markdown preview (basic implementation)\n    const renderMarkdown = (text)=>{\n        return text.replace(/^# (.*$)/gim, '<h1 class=\"text-2xl font-bold text-ghostblue mb-4\">$1</h1>').replace(/^## (.*$)/gim, '<h2 class=\"text-xl font-bold text-white mb-3\">$1</h2>').replace(/^### (.*$)/gim, '<h3 class=\"text-lg font-bold text-zinc-300 mb-2\">$1</h3>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong class=\"font-bold text-white\">$1</strong>').replace(/\\*(.*?)\\*/g, '<em class=\"italic text-zinc-300\">$1</em>').replace(/`(.*?)`/g, '<code class=\"bg-zinc-800 text-flame px-1 rounded\">$1</code>').replace(/\\n/g, '<br>');\n    };\n    const handleKeyDown = (e)=>{\n        // Tab support\n        if (e.key === 'Tab') {\n            e.preventDefault();\n            const start = e.currentTarget.selectionStart;\n            const end = e.currentTarget.selectionEnd;\n            const newContent = content.substring(0, start) + '  ' + content.substring(end);\n            setContent(newContent);\n            // Restore cursor position\n            setTimeout(()=>{\n                if (textareaRef.current) {\n                    textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 2;\n                }\n            }, 0);\n        }\n    };\n    const insertTemplate = (template)=>{\n        const templates = {\n            heading: '# Your Heading Here\\n\\n',\n            list: '- Item 1\\n- Item 2\\n- Item 3\\n\\n',\n            code: '```\\nYour code here\\n```\\n\\n',\n            quote: '> Your quote here\\n\\n',\n            table: '| Column 1 | Column 2 |\\n|----------|----------|\\n| Cell 1   | Cell 2   |\\n\\n'\n        };\n        const insertion = templates[template] || '';\n        const textarea = textareaRef.current;\n        if (textarea) {\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const newContent = content.substring(0, start) + insertion + content.substring(end);\n            setContent(newContent);\n            // Focus and position cursor\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.selectionStart = textarea.selectionEnd = start + insertion.length;\n            }, 0);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-ghostblue text-lg font-bold\",\n                        children: \"✍️ Scroll Editor\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsPreview(!isPreview),\n                                className: \"px-3 py-1 rounded text-xs font-medium transition-colors \".concat(isPreview ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-700 text-white hover:bg-zinc-600'),\n                                children: isPreview ? '📝 Edit' : '👁️ Preview'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    '🔥 Enhance',\n                                    '📋 Summarize',\n                                    '🎨 Format',\n                                    '📊 Analyze'\n                                ].map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleAscend(action.split(' ')[1].toLowerCase()),\n                                        className: \"px-2 py-1 bg-flame hover:bg-flame/80 text-white text-xs rounded font-medium transition-colors\",\n                                        disabled: !content.trim(),\n                                        children: action\n                                    }, action, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-zinc-500\",\n                                children: lastSaved ? \"Saved \".concat(lastSaved.toLocaleTimeString()) : 'Not saved'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-1 mb-3\",\n                children: [\n                    {\n                        label: 'H1',\n                        action: ()=>insertTemplate('heading')\n                    },\n                    {\n                        label: 'List',\n                        action: ()=>insertTemplate('list')\n                    },\n                    {\n                        label: 'Code',\n                        action: ()=>insertTemplate('code')\n                    },\n                    {\n                        label: 'Quote',\n                        action: ()=>insertTemplate('quote')\n                    },\n                    {\n                        label: 'Table',\n                        action: ()=>insertTemplate('table')\n                    }\n                ].map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: tool.action,\n                        className: \"px-2 py-1 bg-zinc-800 hover:bg-zinc-700 text-zinc-300 text-xs rounded transition-colors\",\n                        children: tool.label\n                    }, tool.label, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg overflow-hidden\",\n                children: isPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-4 overflow-y-auto prose prose-invert max-w-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white leading-relaxed\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(content)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: content,\n                    onChange: (e)=>setContent(e.target.value),\n                    onKeyDown: handleKeyDown,\n                    className: \"w-full h-full bg-transparent text-white resize-none outline-none p-4 font-mono text-sm leading-relaxed\",\n                    placeholder: \"Begin your scroll here...  # Welcome to your Scroll Editor  Start writing in **Markdown** format: - Use # for headings - Use **bold** and *italic* text - Create `code snippets` - Make lists and more!  Press Tab for indentation, Ctrl+Enter for preview.\",\n                    spellCheck: false\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 flex items-center justify-between text-xs text-zinc-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Words: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: wordCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Characters: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: charCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Lines: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: lineCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            content.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-ghostblue rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Ready\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n_s(ScrollEditor, \"Y6C8+QC9JVU5z5JWDiqPeOx8ktE=\");\n_c = ScrollEditor;\nvar _c;\n$RefreshReg$(_c, \"ScrollEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ScrollEditor.tsx\n"));

/***/ })

});