/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AnalyticsDashboard.tsx */ \"(app-pages-browser)/./src/components/AnalyticsDashboard.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScribeChatPanel.tsx */ \"(app-pages-browser)/./src/components/ScribeChatPanel.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ScrollEditor.tsx */ \"(app-pages-browser)/./src/components/ScrollEditor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/TemplateSidebar.tsx */ \"(app-pages-browser)/./src/components/TemplateSidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FAnalyticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScribeChatPanel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FScrollEditor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fghost-in-the-wire%2FDocuments%2Faugment-projects%2Fwriteos-scribe-terminal%2Fwriteos-scribe-terminal%2Fsrc%2Fcomponents%2FTemplateSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AnalyticsDashboard.tsx":
/*!***********************************************!*\
  !*** ./src/components/AnalyticsDashboard.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction AnalyticsDashboard() {\n    _s();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentSession, setCurrentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsDashboard.useEffect\": ()=>{\n            // Load analytics data from localStorage\n            loadAnalyticsData();\n            // Start tracking current session\n            startSession();\n            // Update session every minute\n            const interval = setInterval(updateCurrentSession, 60000);\n            return ({\n                \"AnalyticsDashboard.useEffect\": ()=>{\n                    clearInterval(interval);\n                    endSession();\n                }\n            })[\"AnalyticsDashboard.useEffect\"];\n        }\n    }[\"AnalyticsDashboard.useEffect\"], []);\n    const loadAnalyticsData = ()=>{\n        try {\n            const stored = localStorage.getItem('writeos-analytics');\n            if (stored) {\n                setAnalyticsData(JSON.parse(stored));\n            } else {\n                // Initialize with default data\n                const defaultData = {\n                    totalSessions: 0,\n                    totalWordsWritten: 0,\n                    totalTimeSpent: 0,\n                    averageWordsPerSession: 0,\n                    averageSessionLength: 0,\n                    mostProductiveHour: 14,\n                    weeklyProgress: [\n                        0,\n                        0,\n                        0,\n                        0,\n                        0,\n                        0,\n                        0\n                    ],\n                    aiUsageStats: {\n                        enhanceCount: 0,\n                        summarizeCount: 0,\n                        analyzeCount: 0,\n                        chatMessages: 0\n                    }\n                };\n                setAnalyticsData(defaultData);\n                localStorage.setItem('writeos-analytics', JSON.stringify(defaultData));\n            }\n        } catch (error) {\n            console.error('Error loading analytics data:', error);\n        }\n    };\n    const startSession = ()=>{\n        const session = {\n            id: Date.now().toString(),\n            startTime: new Date(),\n            wordCount: 0,\n            charactersTyped: 0,\n            timeSpent: 0,\n            documentsCreated: 0,\n            aiInteractions: 0\n        };\n        setCurrentSession(session);\n    };\n    const updateCurrentSession = ()=>{\n        if (currentSession) {\n            const now = new Date();\n            const timeSpent = Math.floor((now.getTime() - currentSession.startTime.getTime()) / 60000);\n            setCurrentSession((prev)=>prev ? {\n                    ...prev,\n                    timeSpent\n                } : null);\n        }\n    };\n    const endSession = ()=>{\n        if (currentSession && analyticsData) {\n            const updatedData = {\n                ...analyticsData,\n                totalSessions: analyticsData.totalSessions + 1,\n                totalTimeSpent: analyticsData.totalTimeSpent + currentSession.timeSpent,\n                totalWordsWritten: analyticsData.totalWordsWritten + currentSession.wordCount,\n                averageWordsPerSession: Math.round((analyticsData.totalWordsWritten + currentSession.wordCount) / (analyticsData.totalSessions + 1)),\n                averageSessionLength: Math.round((analyticsData.totalTimeSpent + currentSession.timeSpent) / (analyticsData.totalSessions + 1))\n            };\n            setAnalyticsData(updatedData);\n            localStorage.setItem('writeos-analytics', JSON.stringify(updatedData));\n        }\n    };\n    const formatTime = (minutes)=>{\n        const hours = Math.floor(minutes / 60);\n        const mins = minutes % 60;\n        return hours > 0 ? \"\".concat(hours, \"h \").concat(mins, \"m\") : \"\".concat(mins, \"m\");\n    };\n    const getProductivityLevel = ()=>{\n        if (!currentSession) return 'Starting';\n        if (currentSession.timeSpent < 5) return 'Warming up';\n        if (currentSession.timeSpent < 15) return 'Getting focused';\n        if (currentSession.timeSpent < 30) return 'In the zone';\n        if (currentSession.timeSpent < 60) return 'Deep work';\n        return 'Marathon session!';\n    };\n    if (!analyticsData) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsVisible(!isVisible),\n                className: \"bg-flame hover:bg-flame/80 text-white p-3 rounded-full shadow-lg transition-all duration-300 mb-2\",\n                children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-zinc-900 border border-zinc-700 rounded-lg p-4 w-80 shadow-xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-flame font-bold text-lg\",\n                                children: \"\\uD83D\\uDCCA Writing Analytics\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsVisible(false),\n                                className: \"text-zinc-400 hover:text-white\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-3 bg-zinc-800 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-ghostblue font-semibold mb-2\",\n                                children: \"Current Session\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-zinc-400\",\n                                                children: \"Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: getProductivityLevel()\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-zinc-400\",\n                                                children: \"Time:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: formatTime((currentSession === null || currentSession === void 0 ? void 0 : currentSession.timeSpent) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-zinc-400\",\n                                                children: \"Words:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white\",\n                                                children: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.wordCount) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-zinc-800 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-flame\",\n                                                children: analyticsData.totalSessions\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-zinc-400\",\n                                                children: \"Total Sessions\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-zinc-800 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-ghostblue\",\n                                                children: analyticsData.totalWordsWritten.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-zinc-400\",\n                                                children: \"Words Written\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-zinc-800 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: formatTime(analyticsData.totalTimeSpent)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-zinc-400\",\n                                                children: \"Time Spent\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-zinc-800 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: analyticsData.averageWordsPerSession\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-zinc-400\",\n                                                children: \"Avg Words/Session\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-zinc-800 p-3 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"text-ghostblue font-semibold mb-2 text-sm\",\n                                        children: \"AI Assistance\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Enhance:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: analyticsData.aiUsageStats.enhanceCount\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Analyze:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: analyticsData.aiUsageStats.analyzeCount\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Summarize:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: analyticsData.aiUsageStats.summarizeCount\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-zinc-400\",\n                                                        children: \"Chat:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: analyticsData.aiUsageStats.chatMessages\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-zinc-800 p-3 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        className: \"text-flame font-semibold mb-2 text-sm\",\n                                        children: \"\\uD83D\\uDCA1 Insights\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-zinc-300 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• Most productive at \",\n                                                    analyticsData.mostProductiveHour,\n                                                    \":00\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• Average session: \",\n                                                    formatTime(analyticsData.averageSessionLength)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"• \",\n                                                    analyticsData.totalWordsWritten > 1000 ? 'Great progress!' : 'Keep writing!'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/AnalyticsDashboard.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsDashboard, \"RvSlZLiCor9zPeJWLgWRWd8cFn0=\");\n_c = AnalyticsDashboard;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AnalyticsDashboard.tsx\n"));

/***/ })

});