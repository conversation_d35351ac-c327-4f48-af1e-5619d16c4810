"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ScrollEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/ScrollEditor.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScrollEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ScrollEditor() {\n    _s();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isPreview, setIsPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            if (content.trim()) {\n                const timer = setTimeout({\n                    \"ScrollEditor.useEffect.timer\": ()=>{\n                        // Simulate auto-save\n                        localStorage.setItem('scroll-content', content);\n                        setLastSaved(new Date());\n                    }\n                }[\"ScrollEditor.useEffect.timer\"], 2000);\n                return ({\n                    \"ScrollEditor.useEffect\": ()=>clearTimeout(timer)\n                })[\"ScrollEditor.useEffect\"];\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], [\n        content\n    ]);\n    // Load saved content on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const saved = localStorage.getItem('scroll-content');\n            if (saved) {\n                setContent(saved);\n                setLastSaved(new Date());\n            }\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Listen for template loading events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScrollEditor.useEffect\": ()=>{\n            const handleLoadTemplate = {\n                \"ScrollEditor.useEffect.handleLoadTemplate\": (event)=>{\n                    const template = event.detail;\n                    setContent(template.content);\n                    setLastSaved(null); // Reset save status for new template\n                    // Focus the editor\n                    setTimeout({\n                        \"ScrollEditor.useEffect.handleLoadTemplate\": ()=>{\n                            var _textareaRef_current;\n                            (_textareaRef_current = textareaRef.current) === null || _textareaRef_current === void 0 ? void 0 : _textareaRef_current.focus();\n                        }\n                    }[\"ScrollEditor.useEffect.handleLoadTemplate\"], 100);\n                }\n            }[\"ScrollEditor.useEffect.handleLoadTemplate\"];\n            window.addEventListener('loadTemplate', handleLoadTemplate);\n            return ({\n                \"ScrollEditor.useEffect\": ()=>{\n                    window.removeEventListener('loadTemplate', handleLoadTemplate);\n                }\n            })[\"ScrollEditor.useEffect\"];\n        }\n    }[\"ScrollEditor.useEffect\"], []);\n    // Calculate stats\n    const wordCount = content.trim() ? content.trim().split(/\\s+/).length : 0;\n    const charCount = content.length;\n    const lineCount = content.split('\\n').length;\n    // Simple markdown preview (basic implementation)\n    const renderMarkdown = (text)=>{\n        return text.replace(/^# (.*$)/gim, '<h1 class=\"text-2xl font-bold text-ghostblue mb-4\">$1</h1>').replace(/^## (.*$)/gim, '<h2 class=\"text-xl font-bold text-white mb-3\">$1</h2>').replace(/^### (.*$)/gim, '<h3 class=\"text-lg font-bold text-zinc-300 mb-2\">$1</h3>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong class=\"font-bold text-white\">$1</strong>').replace(/\\*(.*?)\\*/g, '<em class=\"italic text-zinc-300\">$1</em>').replace(/`(.*?)`/g, '<code class=\"bg-zinc-800 text-flame px-1 rounded\">$1</code>').replace(/\\n/g, '<br>');\n    };\n    const handleKeyDown = (e)=>{\n        // Tab support\n        if (e.key === 'Tab') {\n            e.preventDefault();\n            const start = e.currentTarget.selectionStart;\n            const end = e.currentTarget.selectionEnd;\n            const newContent = content.substring(0, start) + '  ' + content.substring(end);\n            setContent(newContent);\n            // Restore cursor position\n            setTimeout(()=>{\n                if (textareaRef.current) {\n                    textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 2;\n                }\n            }, 0);\n        }\n    };\n    const insertTemplate = (template)=>{\n        const templates = {\n            heading: '# Your Heading Here\\n\\n',\n            list: '- Item 1\\n- Item 2\\n- Item 3\\n\\n',\n            code: '```\\nYour code here\\n```\\n\\n',\n            quote: '> Your quote here\\n\\n',\n            table: '| Column 1 | Column 2 |\\n|----------|----------|\\n| Cell 1   | Cell 2   |\\n\\n'\n        };\n        const insertion = templates[template] || '';\n        const textarea = textareaRef.current;\n        if (textarea) {\n            const start = textarea.selectionStart;\n            const end = textarea.selectionEnd;\n            const newContent = content.substring(0, start) + insertion + content.substring(end);\n            setContent(newContent);\n            // Focus and position cursor\n            setTimeout(()=>{\n                textarea.focus();\n                textarea.selectionStart = textarea.selectionEnd = start + insertion.length;\n            }, 0);\n        }\n    };\n    const handleAscend = async (action)=>{\n        if (!content.trim() || isProcessing) return;\n        setIsProcessing(true);\n        try {\n            const response = await fetch('/api/ascend', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    content,\n                    action,\n                    context: 'scroll-editor'\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setContent(result.processedContent);\n                setLastSaved(null); // Reset save status\n                // Show success notification (you could add a toast here)\n                console.log('Ascend completed:', result.message);\n                console.log('Suggestions:', result.suggestions);\n            } else {\n                console.error('Ascend failed:', result.error);\n            }\n        } catch (error) {\n            console.error('Ascend error:', error);\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-ghostblue text-lg font-bold\",\n                        children: \"✍️ Scroll Editor\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsPreview(!isPreview),\n                                className: \"px-3 py-1 rounded text-xs font-medium transition-colors \".concat(isPreview ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-700 text-white hover:bg-zinc-600'),\n                                children: isPreview ? '📝 Edit' : '👁️ Preview'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    '🔥 Enhance',\n                                    '📋 Summarize',\n                                    '🎨 Format',\n                                    '📊 Analyze'\n                                ].map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleAscend(action.split(' ')[1].toLowerCase()),\n                                        className: \"px-2 py-1 bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white text-xs rounded font-medium transition-colors\",\n                                        disabled: !content.trim() || isProcessing,\n                                        children: isProcessing ? '⏳' : action\n                                    }, action, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-zinc-500\",\n                                children: lastSaved ? \"Saved \".concat(lastSaved.toLocaleTimeString()) : 'Not saved'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-1 mb-3\",\n                children: [\n                    {\n                        label: 'H1',\n                        action: ()=>insertTemplate('heading')\n                    },\n                    {\n                        label: 'List',\n                        action: ()=>insertTemplate('list')\n                    },\n                    {\n                        label: 'Code',\n                        action: ()=>insertTemplate('code')\n                    },\n                    {\n                        label: 'Quote',\n                        action: ()=>insertTemplate('quote')\n                    },\n                    {\n                        label: 'Table',\n                        action: ()=>insertTemplate('table')\n                    }\n                ].map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: tool.action,\n                        className: \"px-2 py-1 bg-zinc-800 hover:bg-zinc-700 text-zinc-300 text-xs rounded transition-colors\",\n                        children: tool.label\n                    }, tool.label, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg overflow-hidden\",\n                children: isPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-4 overflow-y-auto prose prose-invert max-w-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white leading-relaxed\",\n                        dangerouslySetInnerHTML: {\n                            __html: renderMarkdown(content)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: content,\n                    onChange: (e)=>setContent(e.target.value),\n                    onKeyDown: handleKeyDown,\n                    className: \"w-full h-full bg-transparent text-white resize-none outline-none p-4 font-mono text-sm leading-relaxed\",\n                    placeholder: \"Begin your scroll here...  # Welcome to your Scroll Editor  Start writing in **Markdown** format: - Use # for headings - Use **bold** and *italic* text - Create `code snippets` - Make lists and more!  Press Tab for indentation, Ctrl+Enter for preview.\",\n                    spellCheck: false\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 flex items-center justify-between text-xs text-zinc-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Words: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: wordCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Characters: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: charCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Lines: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white\",\n                                        children: lineCount\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            content.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-ghostblue rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Ready\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScrollEditor.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, this);\n}\n_s(ScrollEditor, \"RY4gjijf6zMgAVQrPlad4F9++uo=\");\n_c = ScrollEditor;\nvar _c;\n$RefreshReg$(_c, \"ScrollEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ScrollEditor.tsx\n"));

/***/ })

});