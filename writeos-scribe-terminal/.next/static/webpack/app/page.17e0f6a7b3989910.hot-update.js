"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ScribeChatPanel.tsx":
/*!********************************************!*\
  !*** ./src/components/ScribeChatPanel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScribeChatPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ScribeChatPanel() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            role: 'assistant',\n            content: 'Welcome to your AI writing assistant. How can I help you craft your scroll today?',\n            timestamp: new Date()\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScribeChatPanel.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ScribeChatPanel.useEffect\"], [\n        messages\n    ]);\n    const sendMessage = async ()=>{\n        if (!input.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: input.trim(),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput('');\n        setIsLoading(true);\n        try {\n            // Simulate AI response (replace with actual AI API call)\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                role: 'assistant',\n                content: generateAIResponse(userMessage.content),\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiResponse\n                ]);\n        } catch (error) {\n            console.error('Error sending message:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const generateAIResponse = (userInput)=>{\n        // Enhanced AI response logic\n        const responses = {\n            writing: [\n                \"I can help you structure your thoughts. What type of document are you working on?\",\n                \"Let's break this down into clear sections. What's your main objective?\",\n                \"Consider starting with an outline. What are the key points you want to cover?\"\n            ],\n            editing: [\n                \"I can help refine your prose. Share a paragraph and I'll suggest improvements.\",\n                \"Let's focus on clarity and flow. What section needs the most work?\",\n                \"Consider your audience - who are you writing for?\"\n            ],\n            ideas: [\n                \"Let's brainstorm! What's the core concept you want to explore?\",\n                \"I can help generate creative angles. What's your starting point?\",\n                \"What if we approached this from a different perspective?\"\n            ],\n            default: [\n                \"I'm here to help with your writing. What would you like to work on?\",\n                \"Let's craft something amazing together. What's on your mind?\",\n                \"I can assist with writing, editing, brainstorming, or structuring. How can I help?\"\n            ]\n        };\n        const input = userInput.toLowerCase();\n        if (input.includes('write') || input.includes('draft')) {\n            return responses.writing[Math.floor(Math.random() * responses.writing.length)];\n        } else if (input.includes('edit') || input.includes('improve') || input.includes('fix')) {\n            return responses.editing[Math.floor(Math.random() * responses.editing.length)];\n        } else if (input.includes('idea') || input.includes('brainstorm') || input.includes('think')) {\n            return responses.ideas[Math.floor(Math.random() * responses.ideas.length)];\n        } else {\n            return responses.default[Math.floor(Math.random() * responses.default.length)];\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-flame text-lg font-bold mb-4\",\n                children: \"\\uD83E\\uDD16 Scribe Assistant\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 bg-scrollbg border border-shadowline rounded-lg p-4 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto mb-4 space-y-3\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-[80%] p-3 rounded-lg text-sm \".concat(message.role === 'user' ? 'bg-flame text-white' : 'bg-zinc-700 text-zinc-100'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs opacity-60 mt-1\",\n                                                children: message.timestamp.toLocaleTimeString([], {\n                                                    hour: '2-digit',\n                                                    minute: '2-digit'\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                }, message.id, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-zinc-700 text-zinc-100 p-3 rounded-lg text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-ghostblue rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Thinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                className: \"flex-1 bg-zinc-800 border border-zinc-700 rounded px-3 py-2 text-white text-sm focus:outline-none focus:border-ghostblue\",\n                                placeholder: \"Ask your scribe assistant...\",\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: sendMessage,\n                                disabled: !input.trim() || isLoading,\n                                className: \"bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded text-sm font-medium transition-colors\",\n                                children: \"Send\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/ScribeChatPanel.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_s(ScribeChatPanel, \"a/3GhHbUQ1u1bp48TjjmheS6EE4=\");\n_c = ScribeChatPanel;\nvar _c;\n$RefreshReg$(_c, \"ScribeChatPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ScribeChatPanel.tsx\n"));

/***/ })

});