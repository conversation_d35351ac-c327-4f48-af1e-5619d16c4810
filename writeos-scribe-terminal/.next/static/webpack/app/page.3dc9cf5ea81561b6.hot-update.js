"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/TemplateSidebar.tsx":
/*!********************************************!*\
  !*** ./src/components/TemplateSidebar.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TemplateSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../.npm/_npx/90eb7c78286cc043/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction TemplateSidebar() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const templates = [\n        {\n            id: '1',\n            name: \"Blank Scroll\",\n            icon: \"📜\",\n            content: \"# New Document\\n\\nStart writing here...\",\n            description: \"Empty document to start fresh\",\n            category: 'writing'\n        },\n        {\n            id: '2',\n            name: \"Technical Doc\",\n            icon: \"⚙️\",\n            content: \"# Technical Documentation\\n\\n## Overview\\n\\n## Requirements\\n\\n## Implementation\\n\\n## Testing\\n\\n## Deployment\",\n            description: \"Structure for technical documentation\",\n            category: 'technical'\n        },\n        {\n            id: '3',\n            name: \"Creative Writing\",\n            icon: \"✨\",\n            content: \"# Story Title\\n\\n*Genre: [Your Genre]*\\n\\n## Characters\\n\\n## Plot Outline\\n\\n## Chapter 1\\n\\nOnce upon a time...\",\n            description: \"Template for creative stories\",\n            category: 'creative'\n        },\n        {\n            id: '4',\n            name: \"Meeting Notes\",\n            icon: \"📝\",\n            content: \"# Meeting Notes\\n\\n**Date:** [Date]\\n**Attendees:** [Names]\\n**Agenda:**\\n\\n## Discussion Points\\n\\n## Action Items\\n\\n## Next Steps\",\n            description: \"Structured meeting documentation\",\n            category: 'business'\n        },\n        {\n            id: '5',\n            name: \"Project Plan\",\n            icon: \"🎯\",\n            content: \"# Project Plan\\n\\n## Objective\\n\\n## Scope\\n\\n## Timeline\\n\\n## Resources\\n\\n## Milestones\\n\\n## Risk Assessment\",\n            description: \"Comprehensive project planning\",\n            category: 'business'\n        },\n        {\n            id: '6',\n            name: \"Blog Post\",\n            icon: \"📰\",\n            content: \"# Blog Post Title\\n\\n*Published: [Date]*\\n*Tags: [tag1, tag2]*\\n\\n## Introduction\\n\\n## Main Content\\n\\n## Conclusion\\n\\n---\\n*What do you think? Leave a comment below!*\",\n            description: \"Blog post structure\",\n            category: 'writing'\n        },\n        {\n            id: '7',\n            name: \"API Documentation\",\n            icon: \"🔌\",\n            content: \"# API Documentation\\n\\n## Endpoints\\n\\n### GET /api/endpoint\\n\\n**Description:** \\n\\n**Parameters:**\\n\\n**Response:**\\n\\n```json\\n{\\n  \\\"status\\\": \\\"success\\\"\\n}\\n```\",\n            description: \"API reference template\",\n            category: 'technical'\n        }\n    ];\n    const categories = [\n        {\n            id: 'all',\n            name: 'All',\n            icon: '📚'\n        },\n        {\n            id: 'writing',\n            name: 'Writing',\n            icon: '✍️'\n        },\n        {\n            id: 'business',\n            name: 'Business',\n            icon: '💼'\n        },\n        {\n            id: 'technical',\n            name: 'Technical',\n            icon: '⚙️'\n        },\n        {\n            id: 'creative',\n            name: 'Creative',\n            icon: '🎨'\n        }\n    ];\n    const filteredTemplates = selectedCategory === 'all' ? templates : templates.filter((t)=>t.category === selectedCategory);\n    const loadTemplate = (template)=>{\n        // Store the template content to be loaded by the editor\n        localStorage.setItem('scroll-content', template.content);\n        // Trigger a custom event to notify the editor\n        window.dispatchEvent(new CustomEvent('loadTemplate', {\n            detail: template\n        }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-flame text-lg font-bold mb-4\",\n                children: \"\\uD83D\\uDCDC Templates\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-zinc-400 mb-2\",\n                        children: \"Categories\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-1\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedCategory(category.id),\n                                className: \"px-2 py-1 rounded text-xs transition-colors \".concat(selectedCategory === category.id ? 'bg-ghostblue text-zinc-900' : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'),\n                                children: [\n                                    category.icon,\n                                    \" \",\n                                    category.name\n                                ]\n                            }, category.id, true, {\n                                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 space-y-2 overflow-y-auto\",\n                children: filteredTemplates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadTemplate(template),\n                        className: \"w-full text-left p-3 rounded-lg bg-zinc-800 hover:bg-zinc-700 border border-zinc-700 hover:border-zinc-600 transition-colors group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg flex-shrink-0\",\n                                    children: template.icon\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white text-sm font-medium group-hover:text-ghostblue transition-colors\",\n                                            children: template.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-zinc-400 text-xs mt-1 line-clamp-2\",\n                                            children: template.description\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this)\n                    }, template.id, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 pt-4 border-t border-zinc-700 space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full bg-ghostblue hover:bg-ghostblue/80 text-zinc-900 py-2 px-4 rounded font-medium text-sm transition-colors\",\n                        children: \"+ New Template\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-full bg-zinc-800 hover:bg-zinc-700 text-white py-2 px-4 rounded font-medium text-sm transition-colors\",\n                        children: \"\\uD83D\\uDCC1 Browse Scrolls\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Documents/augment-projects/writeos-scribe-terminal/writeos-scribe-terminal/src/components/TemplateSidebar.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(TemplateSidebar, \"ka1F1ceqEXioutdx48zEaS3nBME=\");\n_c = TemplateSidebar;\nvar _c;\n$RefreshReg$(_c, \"TemplateSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TemplateSidebar.tsx\n"));

/***/ })

});