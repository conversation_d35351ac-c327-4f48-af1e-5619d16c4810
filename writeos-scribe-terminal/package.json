{"name": "writeos-scribe-terminal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "export": "next export", "deploy": "npm run build && npm run export", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3"}}