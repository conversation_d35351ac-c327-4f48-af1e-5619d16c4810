"use client";

export default function TemplateSidebar() {
  const templates = [
    { name: "Blank Scroll", icon: "📜" },
    { name: "Technical Doc", icon: "⚙️" },
    { name: "Creative Writing", icon: "✨" },
    { name: "Meeting Notes", icon: "📝" },
    { name: "Project Plan", icon: "🎯" },
  ];

  return (
    <div className="h-full flex flex-col">
      <div className="text-flame text-lg font-bold mb-4">📜 Templates</div>
      <div className="flex-1 space-y-2">
        {templates.map((template, index) => (
          <button
            key={index}
            className="w-full text-left p-3 rounded-lg bg-zinc-800 hover:bg-zinc-700 border border-zinc-700 hover:border-zinc-600 transition-colors"
          >
            <div className="flex items-center gap-3">
              <span className="text-lg">{template.icon}</span>
              <span className="text-white text-sm">{template.name}</span>
            </div>
          </button>
        ))}
      </div>
      <div className="mt-4 pt-4 border-t border-zinc-700">
        <button className="w-full bg-ghostblue hover:bg-ghostblue/80 text-zinc-900 py-2 px-4 rounded font-medium text-sm">
          + New Template
        </button>
      </div>
    </div>
  );
}
