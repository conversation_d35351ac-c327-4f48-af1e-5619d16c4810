"use client";

export default function ScribeChatPanel() {
  return (
    <div className="h-full flex flex-col">
      <div className="text-flame text-lg font-bold mb-4">🤖 Scribe Assistant</div>
      <div className="flex-1 bg-scrollbg border border-shadowline rounded-lg p-4 flex flex-col">
        <div className="flex-1 overflow-y-auto mb-4">
          <div className="text-zinc-400 text-sm">
            Welcome to your AI writing assistant. How can I help you craft your scroll today?
          </div>
        </div>
        <div className="flex gap-2">
          <input 
            type="text"
            className="flex-1 bg-zinc-800 border border-zinc-700 rounded px-3 py-2 text-white text-sm"
            placeholder="Ask your scribe assistant..."
          />
          <button className="bg-flame hover:bg-flame/80 text-white px-4 py-2 rounded text-sm font-medium">
            Send
          </button>
        </div>
      </div>
    </div>
  );
}
