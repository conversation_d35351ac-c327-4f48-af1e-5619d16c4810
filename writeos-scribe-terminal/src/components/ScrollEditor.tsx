"use client";

import { useState, useEffect, useRef } from 'react';
import ScrollMetadata from './ScrollMetadata';

export default function ScrollEditor() {
  const [content, setContent] = useState('');
  const [isPreview, setIsPreview] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-save functionality
  useEffect(() => {
    if (content.trim()) {
      const timer = setTimeout(() => {
        // Simulate auto-save
        localStorage.setItem('scroll-content', content);
        setLastSaved(new Date());
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [content]);

  // Load saved content on mount
  useEffect(() => {
    const saved = localStorage.getItem('scroll-content');
    if (saved) {
      setContent(saved);
      setLastSaved(new Date());
    }
  }, []);

  // Listen for template loading events
  useEffect(() => {
    const handleLoadTemplate = (event: CustomEvent) => {
      const template = event.detail;
      setContent(template.content);
      setLastSaved(null); // Reset save status for new template
      // Focus the editor
      setTimeout(() => {
        textareaRef.current?.focus();
      }, 100);
    };

    window.addEventListener('loadTemplate', handleLoadTemplate as EventListener);
    return () => {
      window.removeEventListener('loadTemplate', handleLoadTemplate as EventListener);
    };
  }, []);

  // Calculate stats
  const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
  const charCount = content.length;
  const lineCount = content.split('\n').length;

  // Simple markdown preview (basic implementation)
  const renderMarkdown = (text: string) => {
    return text
      .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-ghostblue mb-4">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold text-white mb-3">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold text-zinc-300 mb-2">$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold text-white">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic text-zinc-300">$1</em>')
      .replace(/`(.*?)`/g, '<code class="bg-zinc-800 text-flame px-1 rounded">$1</code>')
      .replace(/\n/g, '<br>');
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Tab support
    if (e.key === 'Tab') {
      e.preventDefault();
      const start = e.currentTarget.selectionStart;
      const end = e.currentTarget.selectionEnd;
      const newContent = content.substring(0, start) + '  ' + content.substring(end);
      setContent(newContent);

      // Restore cursor position
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.selectionStart = textareaRef.current.selectionEnd = start + 2;
        }
      }, 0);
    }
  };

  const insertTemplate = (template: string) => {
    const templates = {
      heading: '# Your Heading Here\n\n',
      list: '- Item 1\n- Item 2\n- Item 3\n\n',
      code: '```\nYour code here\n```\n\n',
      quote: '> Your quote here\n\n',
      table: '| Column 1 | Column 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |\n\n'
    };

    const insertion = templates[template as keyof typeof templates] || '';
    const textarea = textareaRef.current;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newContent = content.substring(0, start) + insertion + content.substring(end);
      setContent(newContent);

      // Focus and position cursor
      setTimeout(() => {
        textarea.focus();
        textarea.selectionStart = textarea.selectionEnd = start + insertion.length;
      }, 0);
    }
  };

  const handleAscend = async (action: 'enhance' | 'summarize' | 'format' | 'analyze') => {
    if (!content.trim() || isProcessing) return;

    setIsProcessing(true);
    try {
      const response = await fetch('/api/ascend', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content,
          action,
          context: 'scroll-editor'
        }),
      });

      const result = await response.json();

      if (result.success) {
        setContent(result.processedContent);
        setLastSaved(null); // Reset save status

        // Update analytics
        updateAnalytics(action);

        // Show success notification with AI provider info
        console.log('Ascend completed:', result.message);
        console.log('AI Provider:', result.aiProvider || 'Fallback System');
        console.log('Processing Time:', result.metadata?.processingTime || 'N/A');
        console.log('Suggestions:', result.suggestions);

        // You could add a toast notification here
        showNotification(`${action} completed successfully!`, 'success');
      } else {
        console.error('Ascend failed:', result.error);
        showNotification(`${action} failed. Please try again.`, 'error');
      }
    } catch (error) {
      console.error('Ascend error:', error);
      showNotification('Connection error. Please check your internet.', 'error');
    } finally {
      setIsProcessing(false);
    }
  };

  const updateAnalytics = (action: string) => {
    try {
      const stored = localStorage.getItem('writeos-analytics');
      if (stored) {
        const analytics = JSON.parse(stored);
        switch (action) {
          case 'enhance':
            analytics.aiUsageStats.enhanceCount += 1;
            break;
          case 'summarize':
            analytics.aiUsageStats.summarizeCount += 1;
            break;
          case 'analyze':
            analytics.aiUsageStats.analyzeCount += 1;
            break;
        }
        localStorage.setItem('writeos-analytics', JSON.stringify(analytics));
      }
    } catch (error) {
      console.error('Error updating analytics:', error);
    }
  };

  const showNotification = (message: string, type: 'success' | 'error') => {
    // Simple notification system - you could enhance this with a proper toast library
    console.log(`[${type.toUpperCase()}] ${message}`);

    // Create a temporary visual notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-3 rounded-lg text-white z-50 ${
      type === 'success' ? 'bg-green-600' : 'bg-red-600'
    }`;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header with controls */}
      <div className="flex items-center justify-between mb-4">
        <div className="text-ghostblue text-lg font-bold">✍️ Scroll Editor</div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setIsPreview(!isPreview)}
            className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
              isPreview
                ? 'bg-ghostblue text-zinc-900'
                : 'bg-zinc-700 text-white hover:bg-zinc-600'
            }`}
          >
            {isPreview ? '📝 Edit' : '👁️ Preview'}
          </button>

          {/* Ascend Actions */}
          <div className="flex items-center gap-1">
            {['🔥 Enhance', '📋 Summarize', '🎨 Format', '📊 Analyze'].map((action) => (
              <button
                key={action}
                onClick={() => handleAscend(action.split(' ')[1].toLowerCase() as any)}
                className="px-2 py-1 bg-flame hover:bg-flame/80 disabled:bg-zinc-600 disabled:cursor-not-allowed text-white text-xs rounded font-medium transition-colors"
                disabled={!content.trim() || isProcessing}
              >
                {isProcessing ? '⏳' : action}
              </button>
            ))}
          </div>

          <div className="text-xs text-zinc-500">
            {lastSaved ? `Saved ${lastSaved.toLocaleTimeString()}` : 'Not saved'}
          </div>
        </div>
      </div>

      {/* Quick insert toolbar */}
      <div className="flex gap-1 mb-3">
        {[
          { label: 'H1', action: () => insertTemplate('heading') },
          { label: 'List', action: () => insertTemplate('list') },
          { label: 'Code', action: () => insertTemplate('code') },
          { label: 'Quote', action: () => insertTemplate('quote') },
          { label: 'Table', action: () => insertTemplate('table') }
        ].map((tool) => (
          <button
            key={tool.label}
            onClick={tool.action}
            className="px-2 py-1 bg-zinc-800 hover:bg-zinc-700 text-zinc-300 text-xs rounded transition-colors"
          >
            {tool.label}
          </button>
        ))}
      </div>

      {/* Editor/Preview area */}
      <div className="flex-1 bg-scrollbg border border-shadowline rounded-lg overflow-hidden">
        {isPreview ? (
          <div className="h-full p-4 overflow-y-auto prose prose-invert max-w-none">
            <div
              className="text-white leading-relaxed"
              dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
            />
          </div>
        ) : (
          <textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full h-full bg-transparent text-white resize-none outline-none p-4 font-mono text-sm leading-relaxed"
            placeholder="Begin your scroll here...

# Welcome to your Scroll Editor

Start writing in **Markdown** format:
- Use # for headings
- Use **bold** and *italic* text
- Create `code snippets`
- Make lists and more!

Press Tab for indentation, Ctrl+Enter for preview."
            spellCheck={false}
          />
        )}
      </div>

      {/* Stats bar */}
      <div className="mt-3 flex items-center justify-between text-xs text-zinc-500">
        <div className="flex gap-4">
          <span>Words: <span className="text-white">{wordCount}</span></span>
          <span>Characters: <span className="text-white">{charCount}</span></span>
          <span>Lines: <span className="text-white">{lineCount}</span></span>
        </div>
        <div className="flex items-center gap-2">
          {content.trim() && (
            <div className="w-2 h-2 bg-ghostblue rounded-full animate-pulse"></div>
          )}
          <span>Ready</span>
        </div>
      </div>
    </div>
  );
}
