[build]
  # Build command for Next.js
  command = "npm run build"
  
  # Output directory for Next.js static export
  publish = "out"
  
  # Node.js version
  environment = { NODE_VERSION = "18" }

[build.environment]
  # Next.js build optimizations
  NEXT_TELEMETRY_DISABLED = "1"
  NODE_ENV = "production"
  
  # Feature flags for production
  ENABLE_ANALYTICS = "true"
  ENABLE_REAL_AI = "false"

# API Functions (for serverless functions if needed)
[functions]
  directory = "netlify/functions"

# Redirects and rewrites for SPA routing
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Performance headers
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Environment-specific settings
[context.production]
  command = "npm run build && npm run export"
  
[context.deploy-preview]
  command = "npm run build"
  
[context.branch-deploy]
  command = "npm run build"

# Plugin configurations
[[plugins]]
  package = "@netlify/plugin-nextjs"

# Form handling (if needed for contact forms)
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

# Edge functions (for advanced features)
[[edge_functions]]
  function = "ai-proxy"
  path = "/api/ai/*"
